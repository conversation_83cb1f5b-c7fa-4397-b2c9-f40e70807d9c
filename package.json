{"name": "uploaddoc-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.1", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.8.0", "framer-motion": "^12.4.7", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.474.0", "next": "15.1.6", "pdf-lib": "^1.17.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-google-autocomplete": "^2.7.5", "react-hot-toast": "^2.5.2", "react-icons": "^5.4.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.8.3"}}