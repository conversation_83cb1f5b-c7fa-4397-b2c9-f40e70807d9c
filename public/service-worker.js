// public/service-worker.js

// This ensures the service worker activates immediately
self.addEventListener('install', (event) => {
	// console.log('Service worker installing...');
	// Skip the waiting phase
	self.skipWaiting();
});

// When the service worker activates, claim all clients
self.addEventListener('activate', (event) => {
	// console.log('Service worker activating...');
	event.waitUntil(clients.claim());
});

self.addEventListener('push', (event) => {
	const payload = event.data
		? event.data.json()
		: { title: 'New Notification', body: 'You have a new Document!' };

	event.waitUntil(
		self.registration.showNotification(payload.title, {
			body: payload.body,
			icon: '/icon.png',
			data: {
				// Add a URL property to the notification data
				url: 'https://uploaddoc.vercel.app/dashboard',
			},
		}),
	);
});

// Add a new event listener for notification clicks
self.addEventListener('notificationclick', (event) => {
	// Close the notification
	event.notification.close();

	// Get the URL from the notification data
	const url = event.notification.data.url;

	// Open the URL in a new window/tab
	event.waitUntil(clients.openWindow(url));
});
