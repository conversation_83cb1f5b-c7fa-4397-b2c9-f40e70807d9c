// /app/dashboard/page.js
'use client';
import { useContext, useState, useEffect, Suspense } from 'react';
import { AuthContext } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { useAnalytics } from '../context/AnalyticsContext';
import usePageTracking from '../hooks/usePageTracking';
import ProjectList from '@/components/ProjectList';
import Navbar from '@/components/Navbar';
import UserList from '@/components/UserList';
import NotificationBanner from '@/components/NotificationBanner';
import { FaSpinner } from 'react-icons/fa'; // Import a loading icon from react-icons

const DashboardPage = () => {
	const { user, logout } = useContext(AuthContext);
	const { theme } = useTheme();
	const { trackEvent } = useAnalytics();
	const [showUserList, setShowUserList] = useState(false);
	const [notificationStatus, setNotificationStatus] = useState('default');
	const [isLoading, setIsLoading] = useState(true); // Add a loading state

	// Track page view
	usePageTracking('dashboard', {
		user_type: user?.isAdmin ? 'admin' : 'regular',
	});

	useEffect(() => {
		if (user) {
			checkNotificationPermission();
			// Check if the user is an admin
			if (user.isAdmin) {
				setIsLoading(false); // If the user is an admin, stop loading
			} else {
				setIsLoading(false); // If the user is not an admin, stop loading
			}
		} else {
			setIsLoading(false); // If there is no user, stop loading
		}
	}, [user]);

	const checkNotificationPermission = () => {
		if ('Notification' in window) {
			setNotificationStatus(Notification.permission);
		}
	};

	const requestNotificationPermission = async () => {
		if ('Notification' in window && 'serviceWorker' in navigator) {
			try {
				const permission = await Notification.requestPermission();
				setNotificationStatus(permission);

				if (permission === 'granted') {
					// console.log('Notification permission granted.');
					await registerServiceWorker();
				} else {
					console.log('Notification permission denied.');
				}
			} catch (error) {
				console.error('Error requesting notification permission:', error);
			}
		}
	};
	// Helper function to convert base64 to Uint8Array for VAPID key
	const urlBase64ToUint8Array = (base64String) => {
		const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
		const base64 = (base64String + padding)
			.replace(/-/g, '+')
			.replace(/_/g, '/');
		const rawData = window.atob(base64);
		const outputArray = new Uint8Array(rawData.length);
		for (let i = 0; i < rawData.length; ++i) {
			outputArray[i] = rawData.charCodeAt(i);
		}
		return outputArray;
	};

	const saveSubscription = async (subscription) => {
		try {
			// Extract the subscription details properly
			const subscriptionData = {
				endpoint: subscription.endpoint,
				keys: {
					p256dh: btoa(
						String.fromCharCode.apply(
							null,
							new Uint8Array(subscription.getKey('p256dh')),
						),
					),
					auth: btoa(
						String.fromCharCode.apply(
							null,
							new Uint8Array(subscription.getKey('auth')),
						),
					),
				},
			};

			// console.log('Extracted subscription data:', subscriptionData);

			const payload = {
				uid: user.id,
				subscription: subscriptionData,
			};

			const response = await fetch(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/notifications/subscribe`,
				{
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(payload),
				},
			);

			if (!response.ok) {
				throw new Error('Failed to save subscription');
			}
		} catch (error) {
			console.error('Error saving subscription:', error);
		}
	};

	const registerServiceWorker = async () => {
		if ('serviceWorker' in navigator) {
			try {
				// Check if there's already an active service worker
				const existingRegistration =
					await navigator.serviceWorker.getRegistration();

				if (existingRegistration && existingRegistration.active) {
					// console.log('Using existing service worker');

					// Check if we already have a subscription
					const existingSubscription =
						await existingRegistration.pushManager.getSubscription();
					if (existingSubscription) {
						// console.log('Using existing push subscription');
						await saveSubscription(existingSubscription);
						return;
					}

					// Create a new subscription with the existing registration
					const subscription = await existingRegistration.pushManager.subscribe(
						{
							userVisibleOnly: true,
							applicationServerKey: urlBase64ToUint8Array(
								process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
							),
						},
					);

					await saveSubscription(subscription);
					return;
				}

				// Register a new service worker if none exists
				const registration = await navigator.serviceWorker.register(
					'/service-worker.js',
				);

				// Wait for the service worker to be ready before subscribing
				if (registration.installing || registration.waiting) {
					// Service worker is still installing, wait for it to become active
					console.log('Waiting for service worker to activate...');

					const serviceWorker = registration.installing || registration.waiting;

					// Create a promise that resolves when the service worker is activated
					await new Promise((resolve) => {
						serviceWorker.addEventListener('statechange', (e) => {
							if (e.target.state === 'activated') {
								console.log('Service worker activated');
								resolve();
							}
						});
					});
				}

				// Now that the service worker is active, subscribe to push notifications
				const subscription = await registration.pushManager.subscribe({
					userVisibleOnly: true,
					applicationServerKey: urlBase64ToUint8Array(
						process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
					),
				});

				await saveSubscription(subscription);
			} catch (error) {
				console.error('Error registering service worker:', error);
			}
		}
	};

	if (isLoading) {
		// Show a loading spinner while checking if the user is an admin
		return (
			<div className='flex justify-center items-center min-h-screen bg-background'>
				<FaSpinner className='animate-spin text-4xl text-primary' />
			</div>
		);
	}

	if (!user?.isAdmin) {
		return (
			<div className='bg-background min-h-screen'>
				<Navbar />
				<div className='p-4 text-red-500'>Access Denied</div>
			</div>
		);
	}

	const toggleView = () => {
		const newView = !showUserList;
		setShowUserList(newView);

		// Track view toggle event
		trackEvent('dashboard_view_toggle', {
			view_type: newView ? 'users' : 'documents',
		});
	};

	return (
		<div className='bg-background text-text min-h-screen'>
			<Navbar />
			<div className='p-4'>
				{/* Notification Permission Banner */}
				<NotificationBanner
					notificationStatus={notificationStatus}
					requestNotificationPermission={requestNotificationPermission}
				/>

				<div className='flex justify-between items-center mb-4'>
					<h1 className='text-2xl font-bold'>Dashboard</h1>
					{user?.superAdmin && (
						<button
							onClick={toggleView}
							className='bg-primary text-white px-4 py-2 rounded hover:opacity-90 transition-all'>
							{showUserList ? 'View Documents' : 'View Users'}
						</button>
					)}
				</div>
				{showUserList && user?.superAdmin ? <UserList /> : <ProjectList />}
			</div>
		</div>
	);
};

// Wrap the component in Suspense for usePageTracking (which uses useSearchParams)
const DashboardPageWrapper = () => {
	return (
		<Suspense
			fallback={
				<div className='flex justify-center items-center min-h-screen bg-background'>
					<FaSpinner className='animate-spin text-4xl text-primary' />
				</div>
			}>
			<DashboardPage />
		</Suspense>
	);
};

export default DashboardPageWrapper;
