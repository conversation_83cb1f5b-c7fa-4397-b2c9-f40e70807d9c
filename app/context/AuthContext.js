'use client';
import { useRouter } from 'next/navigation';
import { createContext, useState, useEffect, useCallback } from 'react';

export const AuthContext = createContext();
const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL;

export const AuthProvider = ({ children }) => {
	const [user, setUser] = useState(null);
	const router = useRouter();

	// Function to update local storage and user state
	const updateUser = useCallback((newUser) => {
		localStorage.setItem('user', JSON.stringify(newUser));
		setUser(newUser);
	}, []);

	// Function to update token in local storage
	const updateToken = useCallback((newToken) => {
		localStorage.setItem('token', newToken);
	}, []);

	const googleLogin = () => {
		window.location.href = `${BACKEND_URL}/api/auth/google`;
	};

	const handleGoogleCallback = async () => {
		// Try to get the token from the URL query parameters
		let token = new URLSearchParams(window.location.search).get('token');

		if (!token) {
			// If no token in URL, check localStorage
			token = localStorage.getItem('token');
			if (!token) {
				// If token is missing in both places, throw an error.
				throw new Error('No token provided in the callback.');
			}
			// If token was retrieved from localStorage, you may choose
			// to proceed without removing anything from the URL.
		} else {
			// If the token is present in the URL, remove it from the URL and store it.
			window.history.replaceState({}, document.title, window.location.pathname);
			localStorage.setItem('token', token);
		}

		// Now use the token to fetch user data.
		try {
			const res = await fetch(`${BACKEND_URL}/api/auth/status`, {
				headers: { Authorization: `Bearer ${token}` },
			});
			const data = await res.json();

			if (res.ok) {
				updateToken(token);
				updateUser(data.user);
				return data.user;
			} else {
				throw new Error(data.message || 'Failed to fetch user data.');
			}
		} catch (error) {
			console.error('Error in Google callback:', error);
			throw error;
		}
	};

	// Check user status periodically
	useEffect(() => {
		const checkUserStatus = async () => {
			const token = localStorage.getItem('token');
			if (token) {
				try {
					const response = await fetch(`${BACKEND_URL}/api/auth/status`, {
						headers: {
							Authorization: `Bearer ${token}`,
						},
					});
					const data = await response.json();
					if (response.ok) {
						// Only update the user state if there are actual changes
						if (JSON.stringify(user) !== JSON.stringify(data.user)) {
							// console.log(data.user);
							updateUser(data.user);
						}
					} else {
						logout();
					}
				} catch (error) {
					console.error('Error checking user status:', error);
					logout();
				}
			}
		};

		// Run immediately when component mounts
		checkUserStatus();

		// Also set up the interval for periodic checking
		const interval = setInterval(checkUserStatus, 86400000); // Check every 24 hours
		return () => clearInterval(interval);
	}, [updateUser, user]); // Add user to the dependency array

	useEffect(() => {
		const storedUser = localStorage.getItem('user');
		if (storedUser) {
			try {
				const parsedUser = JSON.parse(storedUser);
				setUser(parsedUser);
			} catch (error) {
				console.error('Failed to parse user data from localStorage:', error);
				localStorage.removeItem('user');
			}
		}
	}, []);

	const fetchWithToken = async (url, options = {}) => {
		const token = localStorage.getItem('token');
		const headers = {
			...options.headers,
			Authorization: `Bearer ${token}`,
		};
		return fetch(url, { ...options, headers });
	};

	const login = async (email, password) => {
		try {
			const res = await fetch(`${BACKEND_URL}/api/auth/login`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ email, password }),
			});

			const data = await res.json();

			if (res.ok) {
				updateToken(data.token);
				updateUser(data.user);
			} else {
				throw new Error(data.message || 'Login failed');
			}
		} catch (error) {
			throw error;
		}
	};

	const register = async (name, email, password, matricNumber) => {
		try {
			const res = await fetch(`${BACKEND_URL}/api/auth/register`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ name, email, password, matricNumber }),
			});

			const data = await res.json();

			if (res.ok) {
				if (data.user && data.user.id) {
					updateUser(data.user);
					return { success: true, message: data.message, userId: data.user.id };
				} else if (data.userId) {
					return { success: true, message: data.message, userId: data.userId };
				} else {
					throw new Error('User data or ID is missing in the response.');
				}
			} else {
				throw new Error(data.message || 'Registration failed.');
			}
		} catch (error) {
			console.error('Register error:', error.message);
			return { success: false, message: error.message };
		}
	};

	const verifyEmail = async (userId, otp) => {
		try {
			const res = await fetch(`${BACKEND_URL}/api/auth/verify-email`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ userId, otp }),
			});
			const data = await res.json();
			if (res.ok) {
				return { success: true, message: data.message };
			} else {
				throw new Error(data.message);
			}
		} catch (error) {
			console.error('Verify email error:', error.message);
			return { success: false, message: error.message };
		}
	};

	const forgotPassword = async (email) => {
		try {
			const response = await fetch(`${BACKEND_URL}/api/auth/forgot-password`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ email }),
			});
			const data = await response.json();
			return data;
		} catch (error) {
			throw new Error('Failed to send OTP');
		}
	};

	const resetPassword = async (email, otp, newPassword) => {
		try {
			const response = await fetch(`${BACKEND_URL}/api/auth/reset-password`, {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ email, otp, newPassword }),
			});
			const data = await response.json();
			return data;
		} catch (error) {
			throw new Error('Failed to reset password');
		}
	};

	const logout = () => {
		localStorage.removeItem('token');
		localStorage.removeItem('user');
		setUser(null);
		router.push('/auth/login');
	};
	const getUserId = (user) => {
		if (user && user._id) {
			return user._id;
		} else if (user && user.id) {
			return user.id;
		}
		return null; // Or undefined, depending on your preference
	};

	return (
		<AuthContext.Provider
			value={{
				user,
				login,
				register,
				verifyEmail,
				logout,
				fetchWithToken,
				forgotPassword,
				resetPassword,
				updateUser,
				updateToken,
				getUserId,
				googleLogin, // Expose the googleLogin function
				handleGoogleCallback,
			}}>
			{children}
		</AuthContext.Provider>
	);
};
