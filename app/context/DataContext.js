'use client';
import {
	createContext,
	useState,
	useContext,
	useEffect,
	useCallback,
	useRef,
} from 'react';
import { AuthContext } from './AuthContext';

export const DataContext = createContext();

export const DataProvider = ({ children }) => {
	const { user, fetchWithToken, getUserId } = useContext(AuthContext);
	const [projects, setProjects] = useState([]);
	const [users, setUsers] = useState([]);
	const [adminUsers, setAdminUsers] = useState([]);
	const [loading, setLoading] = useState({
		projects: false,
		users: false,
		admins: false,
	});
	const [error, setError] = useState({
		projects: null,
		users: null,
		admins: null,
	});
	const [lastFetched, setLastFetched] = useState({
		projects: null,
		users: null,
		admins: null,
	});
	const [pagination, setPagination] = useState({
		projects: { currentPage: 1, limit: 10, totalPages: 1, totalCount: 0 },
		users: { currentPage: 1, limit: 10, totalPages: 1, totalCount: 0 },
		admins: { currentPage: 1, limit: 10, totalPages: 1, totalCount: 0 },
	});

	// Track ongoing fetch requests to prevent duplicates
	const pendingFetches = useRef({
		projects: null,
		users: null,
		admins: null,
	});

	const EXCLUDED_EMAIL = '<EMAIL>';
	const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

	// Create a stable reference to state values needed in fetchData
	const stateRef = useRef({
		adminUsers,
		users,
		projects,
		lastFetched,
		pagination,
	});

	// Update ref whenever these states change
	useEffect(() => {
		stateRef.current = {
			adminUsers,
			users,
			projects,
			lastFetched,
			pagination,
		};
	}, [adminUsers, users, projects, lastFetched, pagination]);

	const shouldRefetch = useCallback((dataType) => {
		const lastFetchTime = stateRef.current.lastFetched[dataType];
		if (!lastFetchTime) return true;
		const now = new Date().getTime();
		return now - lastFetchTime > CACHE_DURATION;
	}, []);

	const fetchData = useCallback(
		async (dataType, forceRefresh = false, paginationOptions = null) => {
			// If there's already a pending fetch for this data type, return that promise
			if (pendingFetches.current[dataType]) {
				return pendingFetches.current[dataType];
			}

			// Get current pagination settings for this data type
			const currentPagination = stateRef.current.pagination[dataType];

			// If data is already in cache and we don't need to refresh, return it immediately
			if (
				!forceRefresh &&
				!shouldRefetch(dataType) &&
				(!paginationOptions ||
					(paginationOptions.currentPage === currentPagination.currentPage &&
						paginationOptions.limit === currentPagination.limit))
			) {
				let data;
				if (dataType === 'admins') {
					data = stateRef.current.adminUsers;
				} else if (dataType === 'users') {
					data = stateRef.current.users;
				} else {
					data = stateRef.current.projects;
				}

				return {
					success: true,
					data,
					pagination: currentPagination,
				};
			}

			// Set pagination options to use (from parameter or current state)
			const paginationToUse = paginationOptions || currentPagination;
			const { currentPage, limit } = paginationToUse;

			const fetchFunctions = {
				users: async () => {
					const response = await fetchWithToken(
						`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/users/all?page=${currentPage}&limit=${limit}`,
					);
					const data = await response.json();

					// Handle new response format with users and pagination
					if (data.users && data.pagination) {
						// Update pagination state
						setPagination((prev) => ({
							...prev,
							users: {
								currentPage: data.pagination.currentPage,
								limit: data.pagination.limit,
								totalPages: data.pagination.totalPages,
								totalCount: data.pagination.totalCount,
							},
						}));
						return data.users.filter((u) => u.email !== EXCLUDED_EMAIL);
					}
					// Handle old pagination response format
					else if (data.docs) {
						// Update pagination state
						setPagination((prev) => ({
							...prev,
							users: {
								currentPage: data.page,
								limit: data.limit,
								totalPages: data.totalPages,
								totalCount: data.totalDocs,
							},
						}));
						return data.docs.filter((u) => u.email !== EXCLUDED_EMAIL);
					}

					// Fallback for old API format
					return data.filter((u) => u.email !== EXCLUDED_EMAIL);
				},
				admins: async () => {
					const response = await fetchWithToken(
						`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/users/admins?page=${currentPage}&limit=${limit}`,
					);
					const data = await response.json();

					// Handle new response format with admins and pagination
					if (data.admins && data.pagination) {
						// Update pagination state
						setPagination((prev) => ({
							...prev,
							admins: {
								currentPage: data.pagination.currentPage,
								limit: data.pagination.limit,
								totalPages: data.pagination.totalPages,
								totalCount: data.pagination.totalCount,
							},
						}));
						return data.admins.filter((u) => u.email !== EXCLUDED_EMAIL);
					}
					// Handle old pagination response format
					else if (data.docs) {
						// Update pagination state
						setPagination((prev) => ({
							...prev,
							admins: {
								currentPage: data.page,
								limit: data.limit,
								totalPages: data.totalPages,
								totalCount: data.totalDocs,
							},
						}));
						return data.docs.filter((u) => u.email !== EXCLUDED_EMAIL);
					}

					// Fallback for old API format
					return data.filter((u) => u.email !== EXCLUDED_EMAIL);
				},
				projects: async () => {
					const userId = getUserId(user);
					const url =
						user?.isAdmin && !user?.superAdmin
							? `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/assigned/${userId}?page=${currentPage}&limit=${limit}`
							: `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/all?page=${currentPage}&limit=${limit}`;
					const response = await fetchWithToken(url);
					const data = await response.json();

					// Handle new response format with projects and pagination
					if (data.projects && data.pagination) {
						// Update pagination state
						setPagination((prev) => ({
							...prev,
							projects: {
								currentPage: data.pagination.currentPage,
								limit: data.pagination.limit,
								totalPages: data.pagination.totalPages,
								totalCount: data.pagination.totalCount,
							},
						}));
						return data.projects;
					}
					// Handle old pagination response format
					else if (data.docs) {
						// Update pagination state
						setPagination((prev) => ({
							...prev,
							projects: {
								currentPage: data.page,
								limit: data.limit,
								totalPages: data.totalPages,
								totalCount: data.totalDocs,
							},
						}));
						return data.docs;
					}

					// Fallback for old API format
					return data;
				},
			};

			setLoading((prev) => ({ ...prev, [dataType]: true }));

			// Create the fetch promise
			const fetchPromise = (async () => {
				try {
					const data = await fetchFunctions[dataType]();
					if (dataType === 'users') setUsers(data);
					else if (dataType === 'admins') setAdminUsers(data);
					else if (dataType === 'projects') setProjects(data);

					setLastFetched((prev) => ({
						...prev,
						[dataType]: new Date().getTime(),
					}));
					setError((prev) => ({ ...prev, [dataType]: null }));

					// Get the updated pagination for the response
					const updatedPagination = stateRef.current.pagination[dataType];

					return {
						success: true,
						data,
						pagination: updatedPagination,
					};
				} catch (error) {
					setError((prev) => ({ ...prev, [dataType]: error.message }));
					return { success: false, error: error.message };
				} finally {
					setLoading((prev) => ({ ...prev, [dataType]: false }));
					// Clear the pending fetch when done
					pendingFetches.current[dataType] = null;
				}
			})();

			// Store the promise so we can return it for duplicate calls
			pendingFetches.current[dataType] = fetchPromise;
			return fetchPromise;
		},
		[fetchWithToken, getUserId, user, shouldRefetch],
	);

	// Initial data fetch when user is available
	useEffect(() => {
		// Clear the initialFetch flag when user changes
		// This ensures data is fetched when the user logs in or changes
		if (user) {
			// Fetch admin users for everyone
			fetchData('admins');

			// Only fetch users and projects for logged-in admin users
			if (user?.isAdmin || user?.superAdmin) {
				Promise.all([fetchData('users'), fetchData('projects')]);
			} else {
				// For regular users, just fetch projects
				fetchData('projects');
			}
		}
	}, [user, fetchData]);

	// Update project in state after operations like accept/reject
	const updateProject = useCallback((projectId, updatedData) => {
		setProjects((prev) =>
			prev.map((p) => (p._id === projectId ? { ...p, ...updatedData } : p)),
		);
	}, []);

	// Remove project from state after deletion
	const removeProject = useCallback((projectId) => {
		setProjects((prev) => prev.filter((p) => p._id !== projectId));
	}, []);

	// Update user in state
	const updateUser = useCallback((userId, updatedData) => {
		setUsers((prev) =>
			prev.map((u) => (u._id === userId ? { ...u, ...updatedData } : u)),
		);
	}, []);

	// Function to update pagination settings and fetch data
	const updatePagination = useCallback(
		(dataType, newPagination) => {
			setPagination((prev) => ({
				...prev,
				[dataType]: {
					...prev[dataType],
					...newPagination,
				},
			}));

			// Fetch data with new pagination settings
			return fetchData(dataType, true, {
				...stateRef.current.pagination[dataType],
				...newPagination,
			});
		},
		[fetchData],
	);

	// Add a method to refresh data on demand
	const refreshData = useCallback(
		(dataType) => {
			return fetchData(dataType, true);
		},
		[fetchData],
	);

	return (
		<DataContext.Provider
			value={{
				projects,
				users,
				adminUsers,
				loading,
				error,
				pagination,
				fetchData,
				updateProject,
				removeProject,
				updateUser,
				updatePagination,
				refreshData, // Expose the refresh method
			}}>
			{children}
		</DataContext.Provider>
	);
};

// Custom hook for easier context usage
export const useData = () => useContext(DataContext);
