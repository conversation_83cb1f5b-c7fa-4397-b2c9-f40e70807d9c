'use client';
import { useState, useContext, useEffect, useCallback, Suspense } from 'react';
import { AuthContext } from '@/app/context/AuthContext';
import { useData } from '@/app/context/DataContext';
import { useTheme } from '@/app/context/ThemeContext';
import { FaSpinner, FaUpload } from 'react-icons/fa';
import { useRouter } from 'next/navigation';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { SubmitProjectFormWrapper } from '@/components/SubmitProject/SubmitProjectFormWrapper';
import SubmittedDocuments from '@/components/SubmittedDocuments';
import Navbar from '@/components/Navbar';

const SubmitProjectPage = () => {
	const { fetchWithToken, user, getUserId } = useContext(AuthContext);
	const { adminUsers: admins } = useData();
	const { theme } = useTheme();
	const router = useRouter();

	const [authChecked, setAuthChecked] = useState(false);
	const [showSubmission, setShowSubmission] = useState(true);
	const [userProjects, setUserProjects] = useState([]);
	const [loadingProjects, setLoadingProjects] = useState(false);
	const [errorProjects, setErrorProjects] = useState('');
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [projectToDelete, setProjectToDelete] = useState(null);

	const [pagination, setPagination] = useState({
		currentPage: 1,
		limit: 10,
		totalPages: 1,
		totalCount: 0,
	});

	const BASE = process.env.NEXT_PUBLIC_BACKEND_URL + '/api/projects';

	useEffect(() => {
		if (!user) {
			router.push('/auth/login');
		}
		setAuthChecked(true);
	}, [user, router]);

	const fetchUserProjects = useCallback(async () => {
		const userId = getUserId(user);
		if (!userId) return;

		setLoadingProjects(true);
		setErrorProjects('');
		try {
			const response = await fetchWithToken(
				`${BASE}/student/${userId}?${new URLSearchParams({
					page: pagination.currentPage,
					limit: pagination.limit,
				})}`,
			);
			if (!response.ok) throw new Error('Failed to fetch user projects');
			const data = await response.json();

			if (data.projects && data.pagination) {
				setUserProjects(data.projects);
				setPagination({
					currentPage: data.pagination.currentPage,
					limit: data.pagination.limit,
					totalPages: data.pagination.totalPages,
					totalCount: data.pagination.totalCount,
				});
			} else if (data.docs) {
				setUserProjects(data.docs);
				setPagination({
					currentPage: data.page,
					limit: data.limit,
					totalPages: data.totalPages,
					totalCount: data.totalDocs,
				});
			} else {
				setUserProjects(data);
			}
		} catch (error) {
			setErrorProjects(error.message);
		} finally {
			setLoadingProjects(false);
		}
	}, [
		fetchWithToken,
		getUserId,
		pagination.currentPage,
		pagination.limit,
		user,
		BASE,
	]);

	useEffect(() => {
		if (authChecked && user && getUserId(user)) {
			fetchUserProjects();
		}
	}, [
		authChecked,
		user,
		fetchUserProjects,
		getUserId,
		pagination.currentPage,
		pagination.limit,
	]);

	const handleDelete = async (projectId) => {
		try {
			const response = await fetchWithToken(`${BASE}/${projectId}`, {
				method: 'DELETE',
			});
			if (!response.ok) throw new Error('Failed to delete project');

			if (Array.isArray(userProjects)) {
				setUserProjects(
					userProjects.filter((project) => project._id !== projectId),
				);
			} else if (userProjects.projects) {
				setUserProjects({
					...userProjects,
					projects: userProjects.projects.filter(
						(project) => project._id !== projectId,
					),
				});
			}

			fetchUserProjects();
		} catch (error) {
			console.error('Error deleting project:', error);
		} finally {
			setProjectToDelete(null);
			setDeleteDialogOpen(false);
		}
	};

	if (!authChecked) {
		return (
			<div className='min-h-screen flex items-center justify-center bg-background'>
				<FaSpinner className='animate-spin h-12 w-12 text-primary' />
			</div>
		);
	}

	if (!user) {
		return (
			<div className='min-h-screen flex items-center justify-center bg-background'>
				<p className='text-text'>Not authenticated.</p>
			</div>
		);
	}

	return (
		<Suspense
			fallback={
				<div className='min-h-screen flex items-center justify-center bg-background'>
					<FaSpinner className='animate-spin h-12 w-12 text-primary' />
				</div>
			}>
			<Navbar />
			<div className='min-h-screen flex flex-col items-center bg-background text-text p-4'>
				{showSubmission ? (
					<SubmitProjectFormWrapper
						toggleView={() => setShowSubmission(false)}
						onSubmitSuccess={fetchUserProjects}
					/>
				) : (
					<div className='backdrop-blur-md p-8 rounded-lg shadow-lg w-full max-w-4xl card'>
						<h2 className='text-3xl font-bold mb-2 text-center'>
							Submitted Documents
						</h2>
						<button
							onClick={() => setShowSubmission(true)}
							className='mx-auto flex items-center gap-2 px-4 py-2 mb-6 bg-gradient-to-r from-primary to-accent rounded-full text-sm font-medium text-white hover:opacity-90 transition-all duration-300 group'>
							<FaUpload className='h-4 w-4 group-hover:scale-110 transition-transform' />
							Submit New Document
						</button>
						<SubmittedDocuments
							userProjects={userProjects}
							admins={admins}
							onDelete={(projectId) => {
								setProjectToDelete(projectId);
								setDeleteDialogOpen(true);
							}}
							isLoading={loadingProjects}
							error={errorProjects}
							pagination={pagination}
							onPageChange={(page) => {
								setPagination((prev) => ({ ...prev, currentPage: page }));
							}}
							onLimitChange={(limit) => {
								setPagination((prev) => ({ ...prev, limit, currentPage: 1 }));
							}}
						/>
					</div>
				)}
			</div>

			<AlertDialog
				open={deleteDialogOpen}
				onOpenChange={setDeleteDialogOpen}>
				<AlertDialogContent className='bg-background border-secondary/20 text-text'>
					<AlertDialogHeader>
						<AlertDialogTitle>Are you sure?</AlertDialogTitle>
						<AlertDialogDescription className='text-text/80'>
							This action cannot be undone. This will permanently delete the
							project.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel className='border-secondary/20 hover:bg-primary/5'>
							Cancel
						</AlertDialogCancel>
						<AlertDialogAction
							onClick={() => handleDelete(projectToDelete)}
							className='bg-red-500 hover:bg-red-600'>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</Suspense>
	);
};

export default SubmitProjectPage;
