'use client';
import { useState, useContext, useEffect } from 'react';
import Link from 'next/link';
import { AuthContext } from '@/app/context/AuthContext';
import { useTheme } from '@/app/context/ThemeContext';
import {
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	FaCheckCircle,
	FaExclamationCircle,
	FaEye,
	FaEyeSlash,
	FaQuestionCircle,
} from 'react-icons/fa';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Navbar from '@/components/Navbar';
import { useRouter } from 'next/navigation';

const HelpModal = ({ isOpen, onClose }) => {
	if (!isOpen) return null;

	return (
		<div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
			<div className='bg-background p-6 rounded-lg shadow-lg max-w-md w-full text-text border border-primary/20'>
				<h3 className='text-lg font-bold mb-4'>Help & Support</h3>
				<p className='mb-4'>
					A verification code (OTP) has been sent to your email address. You
					*must* enter this code to log in. If you do not verify your account
					within 5 minutes, it will be deleted. If you entered the wrong email
					address, please wait 5 minutes for the incorrect account to be
					deleted. You can then create a new account with the correct email and
					credentials.
				</p>
				<p className='mb-4'>
					For assistance, please contact{' '}
					<a
						href='mailto:<EMAIL>.'
						className='text-primary hover:underline'>
						<EMAIL>.
					</a>
				</p>
				<button
					onClick={onClose}
					className='w-full bg-primary text-white py-2 rounded-lg hover:opacity-90 transition-all'>
					Close
				</button>
			</div>
		</div>
	);
};

const Register = () => {
	const { register, verifyEmail, logout } = useContext(AuthContext);
	const { theme } = useTheme();
	const [formData, setFormData] = useState({
		name: '',
		email: '',
		password: '',
		matricNumber: '',
		userId: '',
	});
	const [isRegistered, setIsRegistered] = useState(false);
	const [otp, setOtp] = useState('');
	const [isVerified, setIsVerified] = useState(false);
	const [isLoading, setIsLoading] = useState(false);
	const [notification, setNotification] = useState({ type: '', message: '' });
	const [showPassword, setShowPassword] = useState(false);
	const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
	const [countdown, setCountdown] = useState(300); // 5 minutes in seconds
	const router = useRouter();

	useEffect(() => {
		if (isRegistered && countdown > 0) {
			const timer = setInterval(() => {
				setCountdown((prev) => prev - 1);
			}, 1000);

			return () => clearInterval(timer);
		} else if (isRegistered && countdown === 0) {
			// Clear user from localStorage
			localStorage.removeItem('user');
			// Handle account deletion logic here
			setNotification({
				type: 'error',
				message: 'Account deleted due to unverified email.',
			});
			setIsRegistered(false); // Reset to show the registration form
			setFormData({
				name: '',
				email: '',
				password: '',
				matricNumber: '',
				userId: '',
			});
		}
	}, [isRegistered, countdown]);

	const handleChange = (e) => {
		setFormData({ ...formData, [e.target.name]: e.target.value });
	};

	const handleOtpChange = (e) => {
		setOtp(e.target.value);
	};

	const showNotification = (type, message) => {
		setNotification({ type, message });
		setTimeout(() => setNotification({ type: '', message: '' }), 10000);
	};

	const togglePasswordVisibility = () => {
		setShowPassword(!showPassword);
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		setIsLoading(true);

		try {
			const { name, email, password, matricNumber } = formData;
			const result = await register(name, email, password, matricNumber);

			if (result.success) {
				showNotification('success', result.message);
				setIsRegistered(true);
				setFormData((prev) => ({ ...prev, userId: result.userId }));
				setCountdown(300); // Reset countdown to 5 minutes
			} else {
				showNotification('error', result.message);
			}
		} catch (error) {
			showNotification(
				'error',
				'An unexpected error occurred. Please try again.',
			);
		} finally {
			setIsLoading(false);
		}
	};

	const handleVerifyOtp = async (e) => {
		e.preventDefault();
		setIsLoading(true);

		try {
			const result = await verifyEmail(formData.userId, otp);

			if (result.success) {
				showNotification('success', result.message);
				setIsVerified(true);
				setTimeout(() => {
					logout();
				}, 1000);
				setTimeout(() => {
					router.push('/auth/login');
				}, 2000);
			} else {
				showNotification('error', result.message);
			}
		} catch (error) {
			showNotification('error', 'Failed to verify OTP. Please try again.');
		} finally {
			setTimeout(() => {
				logout();
			}, 1000);
			setIsLoading(false);
		}
	};

	const formatTime = (seconds) => {
		const minutes = Math.floor(seconds / 60);
		const secs = seconds % 60;
		return `${minutes}:${secs < 10 ? '0' : ''}${secs}`;
	};

	return (
		<div>
			<Navbar />
			<div className='min-h-screen flex flex-col items-center justify-center bg-background text-text p-4'>
				<div className='backdrop-blur-md p-8 rounded-lg shadow-lg w-96 max-w-full card'>
					<h2 className='text-3xl font-bold mb-6 text-center'>
						{isRegistered ? 'Verify Email' : 'Register'}
					</h2>

					{notification.message && (
						<Alert
							className={`mb-4 ${
								notification.type === 'success'
									? 'bg-green-500 bg-opacity-20 border-green-500'
									: 'bg-red-500 bg-opacity-20 border-red-500'
							}`}>
							{notification.type === 'success' ? (
								<FaCheckCircle className='h-4 w-4' />
							) : (
								<FaExclamationCircle className='h-4 w-4' />
							)}
							<AlertDescription>{notification.message}</AlertDescription>
						</Alert>
					)}

					{!isRegistered ? (
						<form
							onSubmit={handleSubmit}
							className='space-y-4'>
							<input
								type='text'
								name='name'
								placeholder='Full Name'
								value={formData.name}
								onChange={handleChange}
								className='w-full px-4 py-2 rounded-lg bg-background text-text border border-primary focus:outline-none focus:ring-2 focus:ring-primary'
								required
								disabled={isLoading}
							/>
							<input
								type='email'
								name='email'
								placeholder='Enter a valid email address'
								value={formData.email}
								onChange={handleChange}
								className='w-full px-4 py-2 rounded-lg bg-background text-text border border-primary focus:outline-none focus:ring-2 focus:ring-primary'
								required
								disabled={isLoading}
							/>
							<div className='relative'>
								<input
									type={showPassword ? 'text' : 'password'}
									name='password'
									placeholder='Password'
									value={formData.password}
									onChange={handleChange}
									className='w-full px-4 py-2 rounded-lg bg-background text-text border border-primary focus:outline-none focus:ring-2 focus:ring-primary'
									required
									disabled={isLoading}
								/>
								<button
									type='button'
									onClick={togglePasswordVisibility}
									className='absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5'>
									{showPassword ? (
										<FaEyeSlash className='h-5 w-5 text-text/50' />
									) : (
										<FaEye className='h-5 w-5 text-text/50' />
									)}
								</button>
							</div>
							<input
								type='text'
								name='matricNumber'
								placeholder='Phone Number or Matric Number'
								value={formData.matricNumber}
								onChange={handleChange}
								className='w-full px-4 py-2 rounded-lg bg-background text-text border border-primary focus:outline-none focus:ring-2 focus:ring-primary'
								disabled={isLoading}
							/>
							<button
								type='submit'
								disabled={isLoading}
								className='w-full bg-primary hover:opacity-90 text-white font-bold py-2 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center'>
								{isLoading ? (
									<>
										<FaSpinner className='animate-spin mr-2 h-5 w-5' />
										Registering...
									</>
								) : (
									'Register'
								)}
							</button>
						</form>
					) : (
						<form
							onSubmit={handleVerifyOtp}
							className='space-y-4'>
							<div className='relative'>
								<input
									type='text'
									name='otp'
									placeholder='Enter OTP'
									value={otp}
									onChange={handleOtpChange}
									className='w-full px-4 py-2 rounded-lg bg-primary/10 text-text border border-primary/20 focus:outline-none focus:ring-2 focus:ring-primary'
									required
									disabled={isLoading}
								/>
								<button
									type='button'
									onClick={() => setIsHelpModalOpen(true)}
									className='absolute inset-y-0 right-0 pr-3 flex items-center text-sm leading-5'>
									<FaQuestionCircle className='h-5 w-5 text-text/50 hover:text-text/80 cursor-pointer' />
								</button>
							</div>
							<p className='text-center text-text/70'>
								Your account will be deleted in {formatTime(countdown)}
							</p>
							<button
								type='submit'
								disabled={isLoading}
								className='w-full bg-primary hover:opacity-90 text-white font-bold py-2 rounded-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center'>
								{isLoading ? (
									<>
										<FaSpinner className='animate-spin mr-2 h-5 w-5' />
										Verifying...
									</>
								) : (
									'Verify OTP'
								)}
							</button>
						</form>
					)}
					<button
						type='button'
						onClick={() => setIsHelpModalOpen(true)}
						className='pr-3 flex items-center justify-center mx-auto my-5 text-lg leading-5'>
						<FaQuestionCircle className='h-5 w-5 text-text/50 hover:text-text/80 cursor-pointer' />
					</button>
					<p className='mt-4 text-center text-text/80'>
						Already have an account?{' '}
						<Link
							href='/auth/login'
							className='text-primary hover:underline'>
							Login
						</Link>
					</p>
				</div>
			</div>

			<HelpModal
				isOpen={isHelpModalOpen}
				onClose={() => setIsHelpModalOpen(false)}
			/>
		</div>
	);
};

export default Register;
