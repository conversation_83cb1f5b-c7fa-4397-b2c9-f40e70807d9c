@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
	:root {
		--radius: 0.5rem;

		/* Light mode colors */
		--text: #030407;
		--background: #ebf4ff;
		--primary: #444ebb;
		--secondary: #7d86e8;
		--accent: #5461e8;

		/* Light mode shadows */
		--shadow-color: rgba(79, 90, 199, 0.1);
		--card-shadow: 0 8px 20px var(--shadow-color), 0 4px 8px rgba(0, 0, 0, 0.05);
		--hover-shadow: 0 12px 24px var(--shadow-color),
			0 6px 12px rgba(0, 0, 0, 0.08);
		--btn-shadow: 0 4px 8px rgba(84, 97, 232, 0.3);
		--glow: 0 0 15px rgba(84, 97, 232, 0.3);
	}

	.dark {
		/* Dark mode colors */
		--text: #f8f9fc;
		--background: #000914;
		--primary: #444ebb;
		--secondary: #172082;
		--accent: #1724ab;

		/* Dark mode shadows - more vibrant and pronounced */
		--shadow-color: rgba(68, 78, 187, 0.3);
		--card-shadow: 0 8px 20px var(--shadow-color),
			0 2px 8px rgba(84, 97, 232, 0.2);
		--hover-shadow: 0 12px 28px var(--shadow-color),
			0 4px 10px rgba(84, 97, 232, 0.4);
		--btn-shadow: 0 6px 12px rgba(68, 78, 187, 0.5);
		--glow: 0 0 25px rgba(68, 78, 187, 0.4);
	}
}

body {
	font-family: Arial, Helvetica, sans-serif;
	color: var(--text);
	background-color: var(--background);
	transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom 3D shadow classes */
@layer components {
	.card-3d {
		/* box-shadow: var(--card-shadow); */
		background: linear-gradient(
			145deg,
			rgba(var(--primary-rgb), 0.05) 0%,
			rgba(var(--primary-rgb), 0.1) 100%
		);
		border: 1px solid rgba(var(--primary-rgb), 0.1);
		backdrop-filter: blur(10px);
		transition: all 0.4s ease;
	}

	.card-3d:hover {
		box-shadow: var(--hover-shadow);
		transform: translateY(-5px);
	}

	.card {
		/* box-shadow: var(--card-shadow); */
		background: linear-gradient(
			145deg,
			rgba(var(--primary-rgb), 0.05) 0%,
			rgba(var(--primary-rgb), 0.1) 100%
		);
		border: 1px solid rgba(var(--primary-rgb), 0.1);
		backdrop-filter: blur(10px);
		transition: all 0.4s ease;
	}

	.btn-3d {
		box-shadow: var(--btn-shadow);
		transition: all 0.3s ease;
	}

	.btn-3d:hover {
		transform: translateY(-3px);
		box-shadow: var(--hover-shadow);
	}

	.glow-effect {
		position: relative;
	}

	.glow-effect::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		border-radius: inherit;
		box-shadow: var(--glow);
		opacity: 0;
		transition: opacity 0.3s ease;
		z-index: -1;
	}

	.glow-effect:hover::after {
		opacity: 1;
	}
	.shad {
		box-shadow: var(--hover-shadow);
	}
}

:root {
	--primary-rgb: 68, 78, 187; /* RGB values for the primary color */
}

.dark {
	--primary-rgb: 68, 78, 187;
}
.logo-text {
	text-transform: none;
	font-size: 1.5rem;
	line-height: 2rem;
	font-weight: 700;
	text-align: center;
	background-clip: text;
	-webkit-background-clip: text;
	color: transparent;
	/* Improved gradient for light mode - stronger contrast */
	background-image: linear-gradient(to right, var(--text), var(--accent));
}

/* Dark mode override if needed */
.dark .logo-text {
	background-image: linear-gradient(to right, var(--text), var(--primary));
}
