'use client';
import React, { useState, useEffect, useContext, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import { AuthContext } from '../context/AuthContext';
import { useData } from '../context/DataContext';
import Navbar from '@/components/Navbar';
import SearchAndFilter from '@/components/FindProvider/SearchAndFilter';
import SkeletonLoader from '@/components/FindProvider/SkeletonLoader';
import ErrorComponent from '@/components/ErrorComponent';
import ProviderCard from '@/components/FindProvider/ProviderCard';
import NoResultsComponent from '@/components/FindProvider/NoResultsComponent';
import Pagination from '@/components/Pagination';

const FindProvider = () => {
	const router = useRouter();
	const {
		adminUsers,
		loading,
		error,
		fetchData,
		pagination,
		updatePagination,
	} = useData();
	const { user } = useContext(AuthContext);

	const [searchQuery, setSearchQuery] = useState('');
	const [filters, setFilters] = useState({ location: '', priceRange: '' });
	const [showFilters, setShowFilters] = useState(false);
	const [filteredAdmins, setFilteredAdmins] = useState([]);
	const [paginatedAdmins, setPaginatedAdmins] = useState([]);

	useEffect(() => {
		if (!adminUsers) return;

		let filtered = [...adminUsers];

		if (searchQuery.trim() !== '') {
			filtered = filtered.filter(
				(admin) =>
					admin.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
					admin.username?.toLowerCase().includes(searchQuery.toLowerCase()) ||
					admin.printingLocation
						?.toLowerCase()
						.includes(searchQuery.toLowerCase()) ||
					admin.supportContact
						?.toLowerCase()
						.includes(searchQuery.toLowerCase()),
			);
		}

		if (filters.location) {
			filtered = filtered.filter((admin) =>
				admin.printingLocation
					?.toLowerCase()
					.includes(filters.location.toLowerCase()),
			);
		}

		if (filters.priceRange) {
			const [min, max] = filters.priceRange.split('/').map(Number);
			filtered = filtered.filter((admin) => {
				const cost = parseInt(admin.printingCost) || 0;
				if (max) return cost >= min && cost <= max;
				return cost >= min;
			});
		}

		setFilteredAdmins(filtered);
	}, [adminUsers, searchQuery, filters]);

	useEffect(() => {
		setPaginatedAdmins(filteredAdmins);
	}, [filteredAdmins]);

	const handleRetry = () => {
		fetchData('admins', true);
	};

	const resetFilters = () => {
		setFilters({ location: '', priceRange: '' });
		setSearchQuery('');
	};

	const handleRatingUpdated = (adminId, newRating) => {
		if (!user || !user.id) {
			alert('You must be logged in to rate an admin.');
			return;
		}
		if (user.id === adminId) {
			alert('You cannot rate yourself.');
			return;
		}

		setFilteredAdmins((prevAdmins) =>
			prevAdmins.map((admin) =>
				admin._id === adminId
					? { ...admin, rating: newRating, ratedBy: user.name }
					: admin,
			),
		);
		return user.name;
	};

	const handleAdminSelect = (admin) => {
		router.push(`/submit?adminId=${admin._id}`);
	};

	const handlePageChange = (newPage) => {
		updatePagination('admins', { currentPage: newPage });
	};

	const handleLimitChange = (newLimit) => {
		updatePagination('admins', { limit: newLimit, currentPage: 1 });
	};

	return (
		<div>
			<Navbar />
			<div className='min-h-screen bg-background py-12 px-4'>
				<div className='max-w-6xl mx-auto'>
					<header className='mb-10 text-center'>
						<h1 className='text-4xl font-bold text-text mb-4'>
							Find a Provider
						</h1>
						<p className='text-text/80 text-lg max-w-2xl mx-auto'>
							Connect with trusted providers in your area who can help with your
							printing needs.
						</p>
					</header>

					<SearchAndFilter
						searchQuery={searchQuery}
						setSearchQuery={setSearchQuery}
						filters={filters}
						setFilters={setFilters}
						showFilters={showFilters}
						setShowFilters={setShowFilters}
						resetFilters={resetFilters}
					/>

					{loading.admins ? (
						<SkeletonLoader />
					) : error.admins ? (
						<ErrorComponent
							error={error.admins}
							handleRetry={handleRetry}
						/>
					) : (
						<>
							{filteredAdmins.length > 0 ? (
								<>
									<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
										{paginatedAdmins.map((admin) => (
											<ProviderCard
												key={admin._id}
												admin={admin}
												handleAdminSelect={handleAdminSelect}
												handleRatingUpdated={handleRatingUpdated}
											/>
										))}
									</div>

									<Pagination
										pagination={pagination.admins}
										onPageChange={handlePageChange}
										onLimitChange={handleLimitChange}
										itemName='providers'
										currentItems={paginatedAdmins}
									/>
								</>
							) : (
								<NoResultsComponent
									searchQuery={searchQuery}
									setSearchQuery={setSearchQuery}
								/>
							)}
						</>
					)}
				</div>
			</div>
		</div>
	);
};

// Wrap the component in Suspense for useSearchParams
const FindProviderPage = () => {
	return (
		<Suspense
			fallback={
				<div className='min-h-screen flex items-center justify-center bg-background'>
					<div className='animate-spin text-primary'>
						<svg
							className='w-10 h-10'
							xmlns='http://www.w3.org/2000/svg'
							fill='none'
							viewBox='0 0 24 24'>
							<circle
								className='opacity-25'
								cx='12'
								cy='12'
								r='10'
								stroke='currentColor'
								strokeWidth='4'></circle>
							<path
								className='opacity-75'
								fill='currentColor'
								d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
						</svg>
					</div>
				</div>
			}>
			<FindProvider />
		</Suspense>
	);
};

export default FindProviderPage;
