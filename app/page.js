import AnimatedUploadProgress from '@/components/AnimatedUploadProgress';
import Navbar from '@/components/Navbar';
import Underline from '@/components/Underline';
import { Check, Shield, Sparkles, Upload } from 'lucide-react';
import Link from 'next/link';
import {
	FaUpload,
	FaLock,
	FaUserShield,
	FaUsers,
	FaTrash,
	FaArrowRight,
	FaCheckCircle,
} from 'react-icons/fa';

const Home = () => {
	return (
		<div className='min-h-screen bg-background'>
			<Navbar />

			{/* Hero Section */}
			<section className='relative overflow-hidden py-24 md:py-32 px-6'>
				{/* Background elements */}
				<div className='absolute inset-0 opacity-20'>
					<div className='absolute top-20 left-10 w-40 h-40 rounded-full bg-primary blur-3xl'></div>
					<div className='absolute bottom-10 right-10 w-60 h-60 rounded-full bg-secondary blur-3xl'></div>
				</div>

				<div className='relative z-10 max-w-7xl mx-auto'>
					<div className='flex flex-col lg:flex-row items-center'>
						<div className='lg:w-1/2 text-center lg:text-left mb-12 lg:mb-0'>
							<span className='inline-block p-[1px] rounded-full text-blue-700 text-sm font-medium mb-6 animate-fade-in font-work-sans bg-gradient-to-r from-blue-300 to-purple-300 shadow-pink-200 shadow-sm'>
								<span className='flex items-center gap-2 w-full h-full bg-blue-100 px-4 py-1.5 rounded-full font-medium'>
									<Sparkles className='h-4 w-4 text-blue-900' />
									Simplify Your Document Management{' '}
								</span>
							</span>

							<h1 className='text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-text leading-tight'>
								Manage Documents{' '}
								<span className='text-transparent bg-clip-text bg-gradient-to-r from-primary to-accent'>
									Without the Hassle
								</span>
							</h1>
							<span className='block w-full max-w-[200px] sm:max-w-[300px] mx-auto lg:mx-0'>
								<Underline
									width='253'
									height='20'
								/>
							</span>

							<p className='text-lg md:text-xl text-text/80 max-w-xl mb-8 leading-relaxed'>
								Replace WhatsApp file sharing with our secure, dedicated
								platform for seamless document submission and printing.
							</p>

							<div className='flex flex-wrap items-center justify-center lg:justify-start gap-4'>
								<Link
									href='/submit'
									className='btn-3d bg-gradient-to-r from-primary to-accent text-white px-8 py-4 rounded-lg shadow-lg hover:opacity-90 transition-all duration-300 transform hover:scale-105 font-medium flex items-center gap-2'>
									<span>Submit Documents</span>
									<FaArrowRight />
								</Link>

								<Link
									href='https://www.youtube.com/live/Jy5QRfJgTnw?si=jhD3s07qdOrcICuk'
									title='Upload Doc Launch'
									className='glow-effect text-text border border-primary/30 bg-primary/10 hover:bg-primary/20 px-6 py-4 rounded-lg transition-all duration-300 font-medium'>
									How It Works
								</Link>
							</div>

							<div className='mt-8 grid grid-cols-3 gap-4'>
								<div className='flex items-center gap-2 text-text/80'>
									<FaCheckCircle className='text-primary' />
									<span>Secure</span>
								</div>
								<div className='flex items-center gap-2 text-text/80'>
									<FaCheckCircle className='text-primary' />
									<span>Fast</span>
								</div>
								<div className='flex items-center gap-2 text-text/80'>
									<FaCheckCircle className='text-primary' />
									<span>Simple</span>
								</div>
							</div>
						</div>

						<div className='lg:w-1/2 flex justify-center'>
							<div className='card-3d w-full max-w-md rounded-lg p-6'>
								<div className='flex justify-center mb-4'>
									<div className='btn-3d bg-gradient-to-r from-primary to-accent p-4 rounded-full'>
										<FaUpload className='w-5 h-5 text-white' />
									</div>
								</div>

								<h2 className='text-xl font-bold text-center mb-1 text-text'>
									Upload Documents
								</h2>
								<p className='text-text/70 text-center mb-6'>
									Secure and fast document processing
								</p>

								<div className='space-y-4'>
									{/* Completed upload */}
									<div className='card bg-primary/10 p-4 rounded-lg flex items-center'>
										<div className='btn-3d bg-gradient-to-r from-primary to-accent p-2 rounded-full mr-4'>
											<Check className='w-5 h-5 text-white' />
										</div>
										<div>
											<p className='font-medium text-text'>Report-Final.pdf</p>
											<p className='text-text/70 text-sm'>
												Uploaded successfully
											</p>
										</div>
									</div>

									{/* In progress upload */}
									<AnimatedUploadProgress
										fileName='Final Project.pdf'
										initialProgress={10}
									/>

									{/* Security info */}
									<div className='card bg-primary/10 p-4 rounded-lg flex items-center'>
										<div className='btn-3d bg-gradient-to-r from-primary to-accent p-2 rounded-full mr-4'>
											<Shield className='w-5 h-5 text-white' />
										</div>
										<div>
											<p className='font-medium text-text'>Secure Storage</p>
											<p className='text-text/70 text-sm'>
												Accessible only by authorized admins.
											</p>
										</div>
									</div>
								</div>

								{/* Upload button */}
								<Link
									href='/submit'
									className=' w-full bg-gradient-to-r from-primary to-accent text-white rounded-lg py-3 mt-6 flex items-center justify-center hover:opacity-90 transition-all duration-300'>
									<Upload className='w-5 h-5 mr-2' />
									Upload New Document
								</Link>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* Features Section */}
			<section className='py-24 px-6 relative overflow-hidden'>
				<div className='absolute inset-0 bg-gradient-to-b from-transparent via-primary/10 to-transparent'></div>

				<div className='relative z-10 max-w-6xl mx-auto'>
					<div className='text-center mb-16'>
						<span className='inline-block px-4 py-1 rounded-full bg-primary/20 text-primary text-sm font-medium mb-4 glow-effect'>
							Features
						</span>
						<h2 className='text-4xl font-bold mb-6 text-text'>
							Why Choose UploadDoc?
						</h2>
						<p className='text-xl text-text/80 max-w-2xl mx-auto'>
							Our platform offers a complete solution for document management
							and printing services.
						</p>
					</div>

					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
						{/* Feature cards with existing icons */}
						<div className='group relative'>
							<div className='absolute inset-0 bg-gradient-to-r from-primary to-accent rounded-xl opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500'></div>
							<div className='card-3d relative p-8 rounded-xl h-full transition-all duration-300'>
								<div className='btn-3d w-14 h-14 rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center mb-6'>
									<FaUpload className='text-2xl text-white' />
								</div>
								<h3 className='text-2xl font-semibold mb-4 text-text'>
									Quick Submission
								</h3>
								<p className='text-text/80'>
									Upload your documents in just a few clicks and send them
									directly to a printing provider.
								</p>
							</div>
						</div>

						<div className='group relative'>
							<div className='absolute inset-0 bg-gradient-to-r from-primary to-secondary rounded-xl opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500'></div>
							<div className='card-3d relative p-8 rounded-xl h-full transition-all duration-300'>
								<div className='btn-3d w-14 h-14 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center mb-6'>
									<FaLock className='text-2xl text-white' />
								</div>
								<h3 className='text-2xl font-semibold mb-4 text-text'>
									Secure Storage
								</h3>
								<p className='text-text/80'>
									Your documents are securely stored and accessible only by
									authorized admins.
								</p>
							</div>
						</div>

						<div className='group relative'>
							<div className='absolute inset-0 bg-gradient-to-r from-primary to-accent rounded-xl opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500'></div>
							<div className='card-3d relative p-8 rounded-xl h-full transition-all duration-300'>
								<div className='btn-3d w-14 h-14 rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center mb-6'>
									<FaUserShield className='text-2xl text-white' />
								</div>
								<h3 className='text-2xl font-semibold mb-4 text-text'>
									Admin Managed
								</h3>
								<p className='text-text/80'>
									Admins can efficiently review, manage, and delete submissions
									as needed.
								</p>
							</div>
						</div>

						<div className='group relative'>
							<div className='absolute inset-0 bg-gradient-to-r from-primary to-secondary rounded-xl opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500'></div>
							<div className='card-3d relative p-8 rounded-xl h-full transition-all duration-300'>
								<div className='btn-3d w-14 h-14 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center mb-6'>
									<FaUsers className='text-2xl text-white' />
								</div>
								<h3 className='text-2xl font-semibold mb-4 text-text'>
									Become a Provider
								</h3>
								<p className='text-text/80'>
									Request to become a printing provider and allow others to
									submit projects to you.
								</p>
							</div>
						</div>

						<div className='group relative'>
							<div className='absolute inset-0 bg-gradient-to-r from-primary to-accent rounded-xl opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500'></div>
							<div className='card-3d relative p-8 rounded-xl h-full transition-all duration-300'>
								<div className='btn-3d w-14 h-14 rounded-full bg-gradient-to-r from-primary to-accent flex items-center justify-center mb-6'>
									<FaUserShield className='text-2xl text-white' />
								</div>
								<h3 className='text-2xl font-semibold mb-4 text-text'>
									Super Admin Control
								</h3>
								<p className='text-text/80'>
									Super admins oversee the system, manage admin assignments, and
									handle project deletions.
								</p>
							</div>
						</div>

						<div className='group relative'>
							<div className='absolute inset-0 bg-gradient-to-r from-primary to-secondary rounded-xl opacity-0 group-hover:opacity-100 blur-xl transition-opacity duration-500'></div>
							<div className='card-3d relative p-8 rounded-xl h-full transition-all duration-300'>
								<div className='btn-3d w-14 h-14 rounded-full bg-gradient-to-r from-primary to-secondary flex items-center justify-center mb-6'>
									<FaTrash className='text-2xl text-white' />
								</div>
								<h3 className='text-2xl font-semibold mb-4 text-text'>
									Easy Tracking
								</h3>
								<p className='text-text/80'>
									Track your submissions with details like sender and document
									name for better organization.
								</p>
							</div>
						</div>
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className='py-20 px-6 relative'>
				<div className='relative z-10 max-w-4xl mx-auto card-3d bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl p-12'>
					<div className='text-center'>
						<h2 className='text-3xl md:text-4xl font-bold mb-6 text-text'>
							Ready to Simplify Your Document Printing?
						</h2>
						<p className='text-xl text-text/80 mb-8 max-w-2xl mx-auto'>
							Join UploadDoc today and experience a streamlined approach to
							document submission and printing.
						</p>
						<div className='flex flex-wrap justify-center gap-4'>
							<Link
								href='/submit'
								className='btn-3d bg-gradient-to-r from-primary to-accent text-white px-8 py-4 rounded-lg font-medium'>
								Get Started Now
							</Link>
							<Link
								href='/learn-more'
								className='glow-effect bg-primary/10 text-text px-8 py-4 rounded-lg hover:bg-primary/20 transition-all duration-300 font-medium'>
								Learn More
							</Link>
						</div>
					</div>
				</div>
			</section>
		</div>
	);
};

export default Home;
