'use client';
import { useState, useContext, useEffect, useCallback } from 'react';
import { AuthContext } from '@/app/context/AuthContext';
import { useData } from '@/app/context/DataContext';
import { useAnalytics } from '@/app/context/AnalyticsContext';
import { FaSpinner, FaUpload } from 'react-icons/fa';
import { useSearchParams } from 'next/navigation';
import SearchableAdminSelect from '@/components/SearchableAdminSelect';
import { PDFDocument } from 'pdf-lib';
import { Notification as NotificationAlert } from './Notification';
import { FileUpload } from './FileUpload';
import { PricingDisplay } from './PricingDisplay';
import { NotificationPopup } from './NotificationPopup';

const ALLOWED_FILE_TYPES = [
	'application/pdf',
	'application/msword',
	'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
	'text/plain',
	'application/vnd.ms-excel',
	'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
	'image/jpeg',
	'image/png',
];

const DEFAULT_FORM_STATE = {
	title: '',
	file: null,
};

const calculatePagesAndPrice = async (file, admin) => {
	let pages = 0;
	if (file.type === 'application/pdf') {
		const arrayBuffer = await file.arrayBuffer();
		const pdfDoc = await PDFDocument.load(arrayBuffer);
		pages = pdfDoc.getPageCount();
	} else {
		const sizeInMB = file.size / (1024 * 1024);
		pages = Math.ceil(sizeInMB * 2);
	}

	const basePrice = pages * admin.printingCost;
	let finalPrice = basePrice;
	let appliedDiscount = null;

	if (admin.discountRates?.length) {
		const sortedRates = [...admin.discountRates].sort(
			(a, b) => b.discount - a.discount,
		);
		for (const rate of sortedRates) {
			if (
				pages >= rate.minPages &&
				(!rate.maxPages || pages <= rate.maxPages)
			) {
				finalPrice = basePrice * (1 - rate.discount / 100);
				appliedDiscount = {
					percentage: rate.discount,
					savings: (basePrice - finalPrice).toFixed(2),
					description: `${rate.discount}% off for ${rate.minPages}${
						rate.maxPages ? '-' + rate.maxPages : '+'
					} pages`,
				};
				break;
			}
		}
	}

	return {
		pages,
		price: finalPrice.toFixed(2),
		originalPrice: basePrice.toFixed(2),
		discount: appliedDiscount,
	};
};

export const SubmitProjectForm = ({ toggleView, onSubmitSuccess }) => {
	const { fetchWithToken, user, getUserId } = useContext(AuthContext);
	const { adminUsers: admins } = useData();
	const { trackEvent } = useAnalytics();
	const searchParams = useSearchParams();
	const [formData, setFormData] = useState(DEFAULT_FORM_STATE);
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [notification, setNotification] = useState(null);
	const [dragActive, setDragActive] = useState(false);
	const [selectedFileName, setSelectedFileName] = useState('');
	const [selectedAdmin, setSelectedAdmin] = useState('');
	const [notificationStatus, setNotificationStatus] = useState('default');
	const [pricing, setPricing] = useState({
		pages: 0,
		price: 0,
		originalPrice: 0,
		discount: null,
	});

	useEffect(() => {
		const adminId = searchParams.get('adminId');
		if (adminId && adminId !== selectedAdmin) {
			setSelectedAdmin(adminId);
		}
	}, [searchParams, selectedAdmin]);

	useEffect(() => {
		// Check notification permission status when component mounts
		if ('Notification' in window) {
			// Use window.Notification to access the browser's Notification API
			setNotificationStatus(window.Notification.permission);
		}
	}, []);

	const showNotification = useCallback((type, message) => {
		setNotification({ type, message });
		setTimeout(() => setNotification(null), 5000);
	}, []);

	const handleDrag = useCallback((e) => {
		e.preventDefault();
		e.stopPropagation();
		if (e.type === 'dragenter' || e.type === 'dragover') {
			setDragActive(true);
		} else if (e.type === 'dragleave') {
			setDragActive(false);
		}
	}, []);

	const handleDrop = useCallback(
		async (e) => {
			e.preventDefault();
			e.stopPropagation();
			setDragActive(false);
			const file = e.dataTransfer.files?.[0];
			if (file && ALLOWED_FILE_TYPES.includes(file.type)) {
				setFormData((prev) => ({ ...prev, file }));
				setSelectedFileName(file.name);
				const admin = admins.find((a) => a._id === selectedAdmin);
				if (admin) {
					const pricingData = await calculatePagesAndPrice(file, admin);
					setPricing(pricingData);
				}
			} else {
				showNotification(
					'error',
					'Invalid file type. Allowed: PDF, DOC, DOCX, TXT, XLSX, PNG, JPG',
				);
			}
		},
		[selectedAdmin, admins, showNotification],
	);

	const handleFileChange = async (e) => {
		const file = e.target.files?.[0];
		if (!file) return;
		if (ALLOWED_FILE_TYPES.includes(file.type)) {
			setFormData((prev) => ({ ...prev, file }));
			setSelectedFileName(file.name);
			const admin = admins.find((a) => a._id === selectedAdmin);
			if (admin) {
				const pricingData = await calculatePagesAndPrice(file, admin);
				setPricing(pricingData);
			}
		} else {
			showNotification(
				'error',
				'Invalid file type. Allowed: PDF, DOC, DOCX, TXT, XLSX',
			);
			e.target.value = '';
		}
	};

	const clearFile = () => {
		setFormData((prev) => ({ ...prev, file: null }));
		setSelectedFileName('');
		setPricing({ pages: 0, price: 0, originalPrice: 0, discount: null });
		document.getElementById('file-input').value = '';
	};

	const requestNotificationPermission = async () => {
		if ('Notification' in window && 'serviceWorker' in navigator) {
			try {
				// Use window.Notification to access the browser's Notification API
				const permission = await window.Notification.requestPermission();
				setNotificationStatus(permission);

				if (permission === 'granted') {
					// console.log('Notification permission granted.');
					await registerServiceWorker();
				} else {
					console.log('Notification permission denied.');
				}
			} catch (error) {
				console.error('Error requesting notification permission:', error);
			}
		}
	};

	const registerServiceWorker = async () => {
		if ('serviceWorker' in navigator) {
			try {
				// Check if there's already an active service worker
				const existingRegistration =
					await navigator.serviceWorker.getRegistration();

				if (existingRegistration && existingRegistration.active) {
					// console.log('Using existing service worker');
					await subscribeUser(existingRegistration);
					return;
				}

				// Register a new service worker if none exists
				const registration = await navigator.serviceWorker.register(
					'/service-worker.js',
				);

				// Wait for the service worker to be ready before subscribing
				if (registration.installing || registration.waiting) {
					// Service worker is still installing, wait for it to become active
					console.log('Waiting for service worker to activate...');

					const serviceWorker = registration.installing || registration.waiting;

					// Create a promise that resolves when the service worker is activated
					await new Promise((resolve) => {
						serviceWorker.addEventListener('statechange', (e) => {
							if (e.target.state === 'activated') {
								// console.log('Service worker activated');
								resolve();
							}
						});
					});
				}

				// Now that the service worker is active, subscribe to push notifications
				await subscribeUser(registration);
			} catch (err) {
				console.error('ServiceWorker registration failed:', err);
				showNotification('error', 'Failed to register notifications');
			}
		}
	};

	const subscribeUser = async (registration) => {
		try {
			const userId = getUserId(user);
			if (!userId) {
				console.error('User ID not found');
				return;
			}

			// Check if we already have a subscription
			let existingSubscription =
				await registration.pushManager.getSubscription();

			// If we have an existing subscription, try to use it, but if it fails, unsubscribe and create a new one
			if (existingSubscription) {
				try {
					// console.log('Found existing push subscription');

					// Extract the subscription details properly
					const subscriptionData = {
						endpoint: existingSubscription.endpoint,
						keys: {
							p256dh: btoa(
								String.fromCharCode.apply(
									null,
									new Uint8Array(existingSubscription.getKey('p256dh')),
								),
							),
							auth: btoa(
								String.fromCharCode.apply(
									null,
									new Uint8Array(existingSubscription.getKey('auth')),
								),
							),
						},
					};

					// console.log('Extracted subscription data:', subscriptionData);

					// Send the existing subscription to the server
					await fetchWithToken(
						`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/notifications/subscribe`,
						{
							method: 'POST',
							headers: { 'Content-Type': 'application/json' },
							body: JSON.stringify({
								uid: userId,
								subscription: subscriptionData,
							}),
						},
					);

					showNotification('success', 'Subscribed to notifications');
					return;
				} catch (error) {
					console.error(
						'Error with existing subscription, creating new one:',
						error,
					);
					// If there was an error with the existing subscription, unsubscribe and create a new one
					await existingSubscription.unsubscribe();
					existingSubscription = null;
				}
			}

			// Create a new subscription
			// console.log('Creating new push subscription...');
			const subscription = await registration.pushManager.subscribe({
				userVisibleOnly: true,
				applicationServerKey: urlBase64ToUint8Array(
					process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
				),
			});

			// console.log('Push subscription created');

			// Extract the subscription details properly
			const subscriptionData = {
				endpoint: subscription.endpoint,
				keys: {
					p256dh: btoa(
						String.fromCharCode.apply(
							null,
							new Uint8Array(subscription.getKey('p256dh')),
						),
					),
					auth: btoa(
						String.fromCharCode.apply(
							null,
							new Uint8Array(subscription.getKey('auth')),
						),
					),
				},
			};

			console.log('Extracted new subscription data:', subscriptionData);

			// Send the subscription to the server
			await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/notifications/subscribe`,
				{
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({ uid: userId, subscription: subscriptionData }),
				},
			);

			showNotification('success', 'Subscribed to notifications');
		} catch (error) {
			console.error('Subscription failed:', error);
			showNotification('error', 'Failed to subscribe to notifications');
		}
	};

	// Helper function to convert base64 to Uint8Array for VAPID key
	const urlBase64ToUint8Array = (base64String) => {
		const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
		const base64 = (base64String + padding)
			.replace(/-/g, '+')
			.replace(/_/g, '/');
		const rawData = window.atob(base64);
		const outputArray = new Uint8Array(rawData.length);
		for (let i = 0; i < rawData.length; ++i) {
			outputArray[i] = rawData.charCodeAt(i);
		}
		return outputArray;
	};

	const sendNotificationToAdmin = async (
		adminId,
		senderName,
		documentTitle,
	) => {
		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/notifications/send-notification`,
				{
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						recipientUid: adminId,
						senderName,
						messageContent: `A new document titled "${documentTitle}" has been submitted. Pages: ${
							pricing.pages
						}, Price: ₦${pricing.price}${
							pricing.discount
								? ` (includes ${pricing.discount.percentage}% discount)`
								: ''
						}`,
					}),
				},
			);
			if (!response.ok) throw new Error('Failed to send notification');
		} catch (error) {
			console.error('Error sending notification:', error);
		}
	};

	const handleSubmit = async (e) => {
		e.preventDefault();
		if (!formData.file || !selectedAdmin) {
			showNotification('error', 'Please upload a file and select an admin');
			return;
		}

		// Track form submission attempt
		trackEvent('document_submission_started', {
			file_type: formData.file.type,
			file_size: formData.file.size,
			page_count: pricing.pages,
			has_discount: !!pricing.discount,
		});

		setIsSubmitting(true);
		const formDataToSend = new FormData();
		formDataToSend.append('title', formData.title);
		formDataToSend.append('file', formData.file);
		formDataToSend.append('assignedAdmin', selectedAdmin);
		formDataToSend.append('studentId', getUserId(user));
		formDataToSend.append('studentName', user.name);
		formDataToSend.append('pageCount', pricing.pages);
		formDataToSend.append('price', pricing.price);
		if (pricing.discount) {
			formDataToSend.append('originalPrice', pricing.originalPrice);
			formDataToSend.append('discountPercentage', pricing.discount.percentage);
		}

		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/upload`,
				{ method: 'POST', body: formDataToSend },
			);
			const data = await response.json();
			if (!response.ok) throw new Error(data.message || 'Submission failed');

			showNotification('success', 'Project submitted successfully!');
			await sendNotificationToAdmin(selectedAdmin, user.name, formData.title);

			// Track successful submission
			trackEvent('document_submission_success', {
				document_id: data.project?._id,
				admin_id: selectedAdmin,
				page_count: pricing.pages,
				price: pricing.price,
				has_discount: !!pricing.discount,
			});

			setFormData(DEFAULT_FORM_STATE);
			setSelectedFileName('');
			setSelectedAdmin('');
			setPricing({ pages: 0, price: 0, personallyPrice: 0, discount: null });
			document.getElementById('file-input').value = '';

			// Pass the submitted project data to the callback
			onSubmitSuccess?.(
				data.project || {
					_id: data._id,
					title: formData.title,
					assignedAdmin: selectedAdmin,
					status: 'pending',
					createdAt: new Date().toISOString(),
					pageCount: pricing.pages,
					price: pricing.price,
				},
			);
		} catch (error) {
			showNotification('error', error.message || 'An error occurred');
		} finally {
			setIsSubmitting(false);
		}
	};

	return (
		<div className='bg-primary/5 backdrop-blur-md p-8 rounded-lg card w-full max-w-lg'>
			<h2 className='text-3xl font-bold mb-2 text-center'>
				Submit Your Document
			</h2>
			<button
				onClick={toggleView}
				className='mx-auto flex items-center gap-2 px-4 py-2 mb-6 bg-gradient-to-r from-primary to-accent rounded-full text-sm font-medium text-white hover:opacity-90 transition-all duration-300 group'>
				<FaUpload className='h-4 w-4 group-hover:scale-110 transition-transform' />
				Submitted Documents
			</button>
			{notification && (
				<NotificationAlert
					type={notification.type}
					message={notification.message}
				/>
			)}

			{/* Notification Permission Popup */}
			<NotificationPopup onRequestPermission={requestNotificationPermission} />
			<form
				onSubmit={handleSubmit}
				className='space-y-4'>
				<input
					type='text'
					name='title'
					placeholder='Document Title'
					value={formData.title}
					onChange={(e) =>
						setFormData((prev) => ({ ...prev, title: e.target.value }))
					}
					className='w-full px-4 py-2 rounded-lg bg-background text-text focus:outline-none focus:ring-2 focus:ring-accent border border-accent'
					required
					disabled={isSubmitting}
				/>
				<SearchableAdminSelect
					admins={admins}
					selectedAdmin={selectedAdmin}
					onSelect={(adminId) => {
						if (adminId !== selectedAdmin) {
							setSelectedAdmin(adminId);
						}
						const admin = admins.find((a) => a._id === adminId);
						if (admin && formData.file) {
							calculatePagesAndPrice(formData.file, admin).then(setPricing);
						}
					}}
					preSelectedAdminId={selectedAdmin}
				/>
				<FileUpload
					dragActive={dragActive}
					onDrag={handleDrag}
					onDrop={handleDrop}
					onChange={handleFileChange}
					selectedFileName={selectedFileName}
					onClear={clearFile}
					disabled={isSubmitting}
				/>

				<PricingDisplay
					pageCount={pricing.pages}
					price={pricing.price}
					originalPrice={pricing.originalPrice}
					discount={pricing.discount}
					title={formData.title}
				/>
				<button
					type='submit'
					disabled={isSubmitting}
					className='w-full flex items-center justify-center gap-2 px-4 py-2 bg-gradient-to-r from-primary to-accent rounded-lg text-sm font-medium text-white hover:opacity-90 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed box-shadow'>
					{isSubmitting ? (
						<FaSpinner className='animate-spin h-4 w-4' />
					) : (
						<FaUpload className='h-4 w-4' />
					)}
					{isSubmitting ? 'Submitting...' : 'Submit Document'}
				</button>
			</form>
		</div>
	);
};
