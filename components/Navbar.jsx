'use client';
import { useState, useContext, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { AuthContext } from '@/app/context/AuthContext';
import { useTheme } from '@/app/context/ThemeContext';
import {
	FaUserCircle,
	FaBars,
	FaTimes,
	FaUserShield,
	FaSignOutAlt,
	FaUser,
	FaSearchLocation,
	FaSun,
	FaMoon,
} from 'react-icons/fa';
import { IoMdHome, IoMdPaper, IoMdInformationCircle } from 'react-icons/io';
import { MdDashboard, MdOutlineUpgrade } from 'react-icons/md';
import Image from 'next/image';

const Navbar = () => {
	const { user, logout } = useContext(AuthContext);
	const { theme, toggleTheme } = useTheme();
	const [showPopup, setShowPopup] = useState(false);
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const popupRef = useRef(null);
	const userIconRef = useRef(null);
	const mobileMenuRef = useRef(null);
	const mobileMenuButtonRef = useRef(null);
	const pathname = usePathname();

	const togglePopup = () => setShowPopup(!showPopup);
	const toggleMobileMenu = () => setIsMobileMenuOpen(!isMobileMenuOpen);

	useEffect(() => {
		const handleClickOutside = (event) => {
			if (
				showPopup &&
				popupRef.current &&
				userIconRef.current &&
				!popupRef.current.contains(event.target) &&
				!userIconRef.current.contains(event.target)
			) {
				setShowPopup(false);
			}
			if (
				isMobileMenuOpen &&
				mobileMenuRef.current &&
				mobileMenuButtonRef.current &&
				!mobileMenuRef.current.contains(event.target) &&
				!mobileMenuButtonRef.current.contains(event.target)
			) {
				setIsMobileMenuOpen(false);
			}
		};
		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, [showPopup, isMobileMenuOpen]);

	const baseNavLinks = [
		{ href: '/', text: 'Home', icon: <IoMdHome className='text-xl' /> },
		{
			href: '/submit',
			text: 'Submit Document',
			icon: <IoMdPaper className='text-xl' />,
		},
		{
			href: '/about',
			text: 'About',
			icon: <IoMdInformationCircle className='text-xl' />,
		},
		{
			href: '/findprovider',
			text: 'Find Provider',
			icon: <FaSearchLocation className='text-xl' />,
		},
	];

	const navLinks = [
		...baseNavLinks,
		user?.isAdmin || user?.SuperAdmin
			? {
					href: '/dashboard',
					text: 'Dashboard',
					icon: <MdDashboard className='text-xl' />,
			  }
			: user
			? {
					href: '/upgradetoadmin',
					text: 'Upgrade',
					icon: <MdOutlineUpgrade className='text-xl' />,
			  }
			: null,
	].filter(Boolean);

	const getUserBadge = () => {
		if (user?.superAdmin) {
			return (
				<div className='flex items-center gap-1 bg-gradient-to-r from-yellow-500 to-amber-600 text-black text-xs font-bold py-1 px-2 rounded-full'>
					<FaUserShield className='text-xs' />
					<span>SUPER ADMIN</span>
				</div>
			);
		} else if (user?.isAdmin) {
			return (
				<div className='flex items-center gap-1 bg-gradient-to-r from-primary to-accent text-white text-xs font-bold py-1 px-2 rounded-full'>
					<FaUserShield className='text-xs' />
					<span>ADMIN</span>
				</div>
			);
		}
		return null;
	};

	return (
		<nav className='sticky top-0 z-50'>
			<div className='relative bg-primary/10 shad backdrop-blur-md'>
				{/* <div className='absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary via-accent to-primary'></div> */}

				<div className='w-full flex justify-between items-center p-4 md:p-5 backdrop-blur-md'>
					<Link
						href='/'
						className='flex items-center gap-2'>
						<div className='relative'>
							<div className='absolute inset-0 bg-primary blur-sm rounded-full opacity-70'></div>
							<div className='relative w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center'>
								<Image
									src={'/icon.png'}
									width={32}
									height={32}
									className='rounded-full'
									alt='UploadDoc Logo'
									priority
								/>
							</div>
						</div>
						<div className='logo-text'>UploadDoc</div>
					</Link>

					{/* Desktop Navigation with Animated Underline */}
					<div className='hidden md:flex space-x-1 items-center'>
						{navLinks.map((link) => {
							const isActive = pathname === link.href;
							return (
								<Link
									key={link.href}
									href={link.href}
									className={`group relative flex items-center gap-2 text-text hover:text-primary px-4 py-2 rounded-lg transition-all duration-300 hover:bg-primary/5 ${
										isActive ? 'text-primary' : ''
									}`}>
									<span className='text-accent group-hover:text-primary transition-colors'>
										{link.icon}
									</span>
									<span>{link.text}</span>
									{/* Animated Underline */}
									<span
										className={`absolute bottom-0 left-0 h-0.5 bg-primary transition-all duration-300 ease-in-out ${
											isActive ? 'w-full' : 'w-0 group-hover:w-full'
										}`}></span>
								</Link>
							);
						})}
					</div>

					{/* User Section */}
					<div className='hidden md:flex items-center gap-3'>
						{/* Theme Toggle Button */}
						<button
							onClick={toggleTheme}
							className='p-2 rounded-full bg-primary/5 hover:bg-primary/10 cursor-pointer text-text hover:text-primary transition-all duration-300'
							aria-label={
								theme === 'dark'
									? 'Switch to light mode'
									: 'Switch to dark mode'
							}>
							{theme === 'dark' ? (
								<FaSun className='text-xl text-accent' />
							) : (
								<FaMoon className='text-xl text-accent' />
							)}
						</button>

						{user && (
							<div className='flex items-center gap-3 bg-primary/5 py-1.5 px-3 rounded-lg'>
								{getUserBadge()}
								<span className='text-text font-medium'>{user.name}</span>
							</div>
						)}
						<div
							ref={userIconRef}
							onClick={togglePopup}
							className='p-2 rounded-full bg-primary/5 hover:bg-primary/10 cursor-pointer text-text hover:text-primary transition-all duration-300'>
							<FaUserCircle className='text-xl' />
						</div>

						{/* User Popup */}
						{showPopup && (
							<div
								ref={popupRef}
								className='absolute right-4 top-16 w-64 bg-background z-50 shadow-xl rounded-lg overflow-hidden backdrop-blur-md'>
								{user ? (
									<>
										<div className='p-4 border-b border-primary/5'>
											<div className='flex items-center gap-3'>
												<div className='w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center'>
													<FaUser className='text-text' />
												</div>
												<div>
													<div className='text-text font-bold'>{user.name}</div>
													{getUserBadge()}
												</div>
											</div>
										</div>
										<div className='p-2'>
											{user?.isAdmin && (
												<Link
													href='/profile'
													className='flex items-center gap-3 px-4 py-3 text-text hover:text-primary hover:bg-primary/10 rounded-lg transition-all'>
													<FaUser className='text-accent' />
													<span>Profile</span>
												</Link>
											)}

											<button
												onClick={logout}
												className='w-full flex items-center gap-3 px-4 py-3 text-text hover:text-primary hover:bg-primary/10 rounded-lg transition-all'>
												<FaSignOutAlt className='text-accent' />
												<span>Logout</span>
											</button>
										</div>
									</>
								) : (
									<div className='p-2'>
										<Link
											href='/auth/login'
											className='flex items-center gap-3 px-4 py-3 text-text hover:text-primary hover:bg-primary/10 rounded-lg transition-all'>
											<FaUser className='text-accent' />
											<span>Login</span>
										</Link>
										<Link
											href='/auth/register'
											className='flex items-center gap-3 px-4 py-3 text-text hover:text-primary hover:bg-primary/10 rounded-lg transition-all'>
											<FaUserCircle className='text-accent' />
											<span>Register</span>
										</Link>
									</div>
								)}
							</div>
						)}
					</div>

					{/* Mobile Menu Button */}
					<button
						ref={mobileMenuButtonRef}
						onClick={toggleMobileMenu}
						className='md:hidden p-2 rounded-lg bg-primary/5 hover:bg-primary/10 text-text focus:outline-none'>
						{isMobileMenuOpen ? <FaTimes /> : <FaBars />}
					</button>
				</div>
			</div>

			{/* Mobile Menu - Slide from right */}
			<div
				ref={mobileMenuRef}
				className={`fixed top-0 right-0 h-full w-72 bg-background border-l border-primary/5 shadow-xl transform transition-transform duration-300 ease-in-out ${
					isMobileMenuOpen ? 'translate-x-0' : 'translate-x-full'
				} md:hidden z-50`}>
				<div className='flex flex-col h-full'>
					{/* Header with close button */}
					<div className='flex items-center justify-between p-5 border-b border-primary/5'>
						<div className='flex items-center gap-2'>
							<div className='w-8 h-8 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center'>
								<IoMdPaper className='text-text text-lg' />
							</div>
							<div className='text-xl font-bold text-text'>UploadDoc</div>
						</div>
						<button
							onClick={toggleMobileMenu}
							className='p-2 rounded-full bg-primary/5 text-text focus:outline-none'>
							<FaTimes />
						</button>
					</div>

					{/* User info if logged in */}
					{user && (
						<Link
							href='/profile'
							className='p-5 border-b border-primary/5'>
							<div className='flex items-center gap-3 mb-2'>
								<div className='w-12 h-12 bg-gradient-to-br from-primary to-accent rounded-full flex items-center justify-center'>
									<FaUser className='text-text text-lg' />
								</div>
								<div>
									<div className='text-text font-bold text-lg'>{user.name}</div>
									{getUserBadge()}
								</div>
							</div>
						</Link>
					)}

					{/* Navigation links */}
					<div className='flex-1 overflow-y-auto p-4'>
						{navLinks.map((link) => (
							<Link
								key={link.href}
								href={link.href}
								onClick={() => setIsMobileMenuOpen(false)}
								className='flex items-center gap-3 text-text hover:text-primary p-3 mb-2 rounded-lg hover:bg-primary/5 transition-all'>
								<div className='w-8 h-8 flex items-center justify-center bg-gradient-to-br from-primary/20 to-accent/20 rounded-full'>
									{link.icon}
								</div>
								<span>{link.text}</span>
							</Link>
						))}
					</div>

					{/* Theme toggle */}
					<div className='p-4 border-t border-primary/5'>
						<button
							onClick={toggleTheme}
							className='w-full flex items-center justify-center gap-2 bg-primary/5 hover:bg-primary/10 text-text py-3 px-4 rounded-lg transition-all mb-4'>
							{theme === 'dark' ? (
								<>
									<FaSun className='text-accent' />
									<span>Switch to Light Mode</span>
								</>
							) : (
								<>
									<FaMoon className='text-accent' />
									<span>Switch to Dark Mode</span>
								</>
							)}
						</button>
					</div>

					{/* Footer with auth links */}
					<div className='p-4 border-t border-primary/5'>
						{user ? (
							<button
								onClick={() => {
									logout();
									setIsMobileMenuOpen(false);
								}}
								className='w-full flex items-center justify-center gap-2 bg-primary/10 hover:bg-primary/20 text-text py-3 px-4 rounded-lg transition-all'>
								<FaSignOutAlt />
								<span>Logout</span>
							</button>
						) : (
							<div className='flex gap-2'>
								<Link
									href='/auth/login'
									onClick={() => setIsMobileMenuOpen(false)}
									className='flex-1 flex items-center justify-center gap-2 bg-primary hover:bg-accent text-text py-3 px-4 rounded-lg transition-all'>
									<FaUser />
									<span>Login</span>
								</Link>
								<Link
									href='/auth/register'
									onClick={() => setIsMobileMenuOpen(false)}
									className='flex-1 flex items-center justify-center gap-2 bg-accent hover:bg-primary text-text py-3 px-4 rounded-lg transition-all'>
									<FaUserCircle />
									<span>Register</span>
								</Link>
							</div>
						)}
					</div>
				</div>
			</div>
		</nav>
	);
};

export default Navbar;
