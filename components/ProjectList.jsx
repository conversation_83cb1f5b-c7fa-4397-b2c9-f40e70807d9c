'use client';
import { AuthContext } from '@/app/context/AuthContext';
import { DataContext } from '@/app/context/DataContext';
import { useState, useEffect, useContext, useMemo, useCallback } from 'react';
import debounce from 'lodash/debounce';
import {
	FaBell,
	FaBellSlash,
	FaProjectDiagram,
	FaSearch,
	FaSync,
	FaSpinner,
	FaDownload,
	FaEye,
	FaCheck,
	FaTimes,
	FaTrash,
	FaSort,
	FaFilter,
} from 'react-icons/fa';
import Pagination from '@/components/Pagination';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import AdminStats from './AdminStats';
import toast from 'react-hot-toast';

const MIME_TO_FORMAT = {
	'application/pdf': 'PDF',
	'application/msword': 'DOC',
	'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
		'DOCX',
	'text/plain': 'TXT',
	'application/vnd.ms-excel': 'XLS',
	'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'XLSX',
	'image/jpeg': 'JPG',
	'image/png': 'PNG',
	'image/gif': 'GIF',
	'application/octet-stream': 'Unknown', // Fallback for unknown types
};

const EXCLUDED_EMAIL = '<EMAIL>';

const ProjectList = () => {
	const { user, fetchWithToken, updateUser, getUserId } =
		useContext(AuthContext);
	const {
		projects,
		users,
		adminUsers,
		loading,
		error,
		fetchData,
		updateProject,
		removeProject,
		pagination,
		updatePagination,
	} = useContext(DataContext);

	// State declarations
	const [projectPagination, setProjectPagination] = useState({
		currentPage: 1,
		limit: 10,
		totalPages: 1,
		totalCount: 0,
	});
	const [searchTerm, setSearchTerm] = useState('');
	const [sortOrder, setSortOrder] = useState('desc');
	const [statusFilter, setStatusFilter] = useState('all');
	const [projectToDelete, setProjectToDelete] = useState(null);
	const [projectToReject, setProjectToReject] = useState(null);
	const [isSubscribed, setIsSubscribed] = useState(false);
	const [refreshing, setRefreshing] = useState(false);
	const [processingProjects, setProcessingProjects] = useState({});
	const [localLoading, setLocalLoading] = useState(true);

	// Utility functions for notifications
	const urlBase64ToUint8Array = (base64String) => {
		const padding = '='.repeat((4 - (base64String.length % 4)) % 4);
		const base64 = (base64String + padding)
			.replace(/-/g, '+')
			.replace(/_/g, '/');
		const rawData = window.atob(base64);
		const outputArray = new Uint8Array(rawData.length);
		for (let i = 0; i < rawData.length; ++i) {
			outputArray[i] = rawData.charCodeAt(i);
		}
		return outputArray;
	};

	// Debounced search handler
	const debouncedSearch = useCallback(
		debounce((value) => setSearchTerm(value), 300),
		[],
	);

	// Create a memoized mapping of admin IDs to names
	const adminNameById = useMemo(() => {
		const map = new Map();
		if (Array.isArray(adminUsers)) {
			adminUsers.forEach((u) => {
				if (u && u._id) map.set(u._id, u.name);
			});
		}
		return map;
	}, [adminUsers]);

	// Filtered projects based on search and filter settings
	const filteredProjects = useMemo(() => {
		if (!Array.isArray(projects)) return [];

		let result = projects.filter(
			(project) =>
				project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
				project.studentName.toLowerCase().includes(searchTerm.toLowerCase()),
		);
		if (statusFilter !== 'all') {
			result = result.filter((project) => project.status === statusFilter);
		}
		return result.sort((a, b) => {
			const dateA = new Date(a.createdAt);
			const dateB = new Date(b.createdAt);
			return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
		});
	}, [projects, searchTerm, statusFilter, sortOrder]);

	// Notification subscription status check
	const checkSubscriptionStatus = async () => {
		try {
			const registration = await navigator.serviceWorker.ready;
			const subscription = await registration.pushManager.getSubscription();
			setIsSubscribed(!!subscription);
		} catch (error) {
			console.error('Error checking subscription:', error);
		}
	};

	// Subscribe to notifications
	const subscribeUser = async (registration) => {
		try {
			const permission = await Notification.requestPermission();
			if (permission !== 'granted') return;

			const userId = getUserId(user);
			if (!userId) {
				console.error('User ID not found');
				return;
			}

			// Check if we already have a subscription
			let existingSubscription =
				await registration.pushManager.getSubscription();

			// If we have an existing subscription, try to use it, but if it fails, unsubscribe and create a new one
			if (existingSubscription) {
				try {
					// console.log('Found existing push subscription');

					// Extract the subscription details properly
					const subscriptionData = {
						endpoint: existingSubscription.endpoint,
						keys: {
							p256dh: btoa(
								String.fromCharCode.apply(
									null,
									new Uint8Array(existingSubscription.getKey('p256dh')),
								),
							),
							auth: btoa(
								String.fromCharCode.apply(
									null,
									new Uint8Array(existingSubscription.getKey('auth')),
								),
							),
						},
					};

					// console.log('Extracted subscription data:', subscriptionData);

					// Send the existing subscription to the server
					await fetchWithToken(
						`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/notifications/subscribe`,
						{
							method: 'POST',
							headers: { 'Content-Type': 'application/json' },
							body: JSON.stringify({
								uid: userId,
								subscription: subscriptionData,
							}),
						},
					);

					setIsSubscribed(true);
					toast.success('Subscribed to notifications');
					return;
				} catch (error) {
					console.error(
						'Error with existing subscription, creating new one:',
						error,
					);
					// If there was an error with the existing subscription, unsubscribe and create a new one
					await existingSubscription.unsubscribe();
					existingSubscription = null;
				}
			}

			// Create a new subscription
			// console.log('Creating new push subscription...');
			const subscription = await registration.pushManager.subscribe({
				userVisibleOnly: true,
				applicationServerKey: urlBase64ToUint8Array(
					process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
				),
			});

			// console.log('Push subscription created');

			// Extract the subscription details properly
			const subscriptionData = {
				endpoint: subscription.endpoint,
				keys: {
					p256dh: btoa(
						String.fromCharCode.apply(
							null,
							new Uint8Array(subscription.getKey('p256dh')),
						),
					),
					auth: btoa(
						String.fromCharCode.apply(
							null,
							new Uint8Array(subscription.getKey('auth')),
						),
					),
				},
			};

			// console.log('Extracted new subscription data:', subscriptionData);

			// Send the subscription to the server
			await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/notifications/subscribe`,
				{
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({ uid: userId, subscription: subscriptionData }),
				},
			);

			setIsSubscribed(true);
			toast.success('Subscribed to notifications');
		} catch (error) {
			console.error('Subscription failed:', error);
			toast.error('Failed to subscribe to notifications');
		}
	};

	// Register service worker for notifications
	const registerServiceWorker = async () => {
		if ('serviceWorker' in navigator) {
			try {
				// Check if there's already an active service worker
				const existingRegistration =
					await navigator.serviceWorker.getRegistration();

				if (existingRegistration && existingRegistration.active) {
					// console.log('Using existing service worker');
					await subscribeUser(existingRegistration);
					return;
				}

				// Register a new service worker if none exists
				const registration = await navigator.serviceWorker.register(
					'/service-worker.js',
				);

				// Wait for the service worker to be ready before subscribing
				if (registration.installing || registration.waiting) {
					// Service worker is still installing, wait for it to become active
					// console.log('Waiting for service worker to activate...');

					const serviceWorker = registration.installing || registration.waiting;

					// Create a promise that resolves when the service worker is activated
					await new Promise((resolve) => {
						serviceWorker.addEventListener('statechange', (e) => {
							if (e.target.state === 'activated') {
								// console.log('Service worker activated');
								resolve();
							}
						});
					});
				}

				// Now that the service worker is active, subscribe to push notifications
				await subscribeUser(registration);
			} catch (err) {
				console.error('ServiceWorker registration failed:', err);
				toast.error('Failed to register notifications');
			}
		}
	};

	// Unsubscribe from notifications
	const handleUnsubscribe = async () => {
		try {
			const registration = await navigator.serviceWorker.ready;
			const subscription = await registration.pushManager.getSubscription();
			if (subscription) {
				const userId = getUserId(user);
				await subscription.unsubscribe();
				await fetchWithToken(
					`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/notifications/unsubscribe`,
					{
						method: 'POST',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify({
							uid: userId,
							endpoint: subscription.endpoint,
						}),
					},
				);
				setIsSubscribed(false);
				toast.success('Unsubscribed from notifications');
			}
		} catch (error) {
			console.error('Unsubscription failed:', error);
			toast.error('Failed to unsubscribe');
		}
	};

	// Update local pagination state when context pagination changes
	useEffect(() => {
		if (pagination && pagination.projects) {
			setProjectPagination(pagination.projects);
		}
	}, [pagination]);

	// Handle page change
	const handlePageChange = (page) => {
		updatePagination('projects', { currentPage: page });
	};

	// Handle limit change
	const handleLimitChange = (limit) => {
		updatePagination('projects', { limit, currentPage: 1 });
	};

	// Initialize data and check notification status
	useEffect(() => {
		const initializeData = async () => {
			try {
				// Check notification subscription status
				if ('serviceWorker' in navigator) {
					checkSubscriptionStatus();
				}
			} catch (error) {
				console.error('Failed to check notification status:', error);
			} finally {
				// Set loading to false regardless - data will be loaded by DataContext
				setLocalLoading(false);
			}
		};

		if (user) {
			initializeData();
		}
	}, [user]);

	// Refresh data handler
	const handleRefreshData = async () => {
		setRefreshing(true);
		try {
			await Promise.all([
				fetchData('projects', true), // Force refresh
				fetchData('users', true), // Force refresh
			]);
			toast.success('Data refreshed successfully');
		} catch (error) {
			toast.error('Failed to refresh data');
		} finally {
			setRefreshing(false);
		}
	};

	// Accept project handler
	const handleAccept = async (projectId) => {
		setProcessingProjects((prev) => ({ ...prev, [projectId]: 'accepting' }));
		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/accept/${projectId}`,
				{ method: 'PUT' },
			);
			if (!response.ok) throw new Error('Failed to accept project');
			const userId = getUserId(user);
			const updatedUserResponse = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/users/${userId}`,
			);
			if (!updatedUserResponse.ok) throw new Error('Failed to update user');
			const updatedUserData = await updatedUserResponse.json();
			updateUser({ ...user, ...updatedUserData });

			// Update project in context
			updateProject(projectId, { status: 'accepted' });
			toast.success('Project accepted');
		} catch (error) {
			toast.error(error.message);
		} finally {
			setProcessingProjects((prev) => ({ ...prev, [projectId]: null }));
		}
	};

	// Delete project handler
	const handleDelete = async (projectId) => {
		setProcessingProjects((prev) => ({ ...prev, [projectId]: 'deleting' }));
		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/projects/${projectId}`,
				{ method: 'DELETE' },
			);
			if (!response.ok) throw new Error('Failed to delete project');

			// Remove project from context
			removeProject(projectId);
			setProjectToDelete(null);
			toast.success('Project deleted');
		} catch (error) {
			toast.error(error.message);
		} finally {
			setProcessingProjects((prev) => ({ ...prev, [projectId]: null }));
		}
	};

	// Handle project rejection - reuses delete handler
	const handleReject = async (projectId) => {
		await handleDelete(projectId);
		setProjectToReject(null);
	};

	// Download file handler
	const handleDownload = async (fileUrl, matricNumber, title, fileType) => {
		try {
			const response = await fetch(fileUrl);
			if (!response.ok) throw new Error('Failed to download file');
			const blob = await response.blob();
			const url = window.URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.href = url;
			link.download = `${title}_${matricNumber}.${
				MIME_TO_FORMAT[fileType] || 'pdf'
			}`.toLowerCase();
			document.body.appendChild(link);
			link.click();
			document.body.removeChild(link);
			window.URL.revokeObjectURL(url);
			toast.success('Download started');
		} catch (error) {
			console.error('Download failed:', error);
			toast.error('Failed to download file');
		}
	};

	// View file handler
	const handleView = (fileUrl, fileType) => {
		if (fileType === 'application/pdf') {
			window.open(fileUrl, '_blank');
		} else {
			toast('File type not viewable in browser. Downloading instead.');
			handleDownload(fileUrl, 'unknown', 'document', fileType);
		}
	};

	// Get document count for logged in admin
	const getLoggedInAdminProjectCount = () => {
		if (!Array.isArray(users)) return 0;
		const userId = getUserId(user);
		const loggedInUserData = users.find((u) => u._id === userId);
		return loggedInUserData ? loggedInUserData.documentsReceived || 0 : 0;
	};

	// Format date for display
	const formatDate = (dateString) =>
		new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit',
		});

	// Get CSS class for status badge
	const getStatusBadgeClass = (status) => {
		const classes = {
			pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
			accepted:
				'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800',
			rejected:
				'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800',
		};
		return (
			classes[status] ||
			'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700'
		);
	};

	// Show loading state only during initial load
	if (localLoading) {
		return (
			<div className='flex justify-center items-center min-h-[400px]'>
				<FaSpinner className='animate-spin text-4xl text-primary' />
			</div>
		);
	}

	// Show error state if there's an error
	if (error && (error.projects || error.users)) {
		return (
			<div className='flex flex-col items-center justify-center min-h-[400px]'>
				<div className='text-red-500 dark:text-red-400 mb-4'>
					Failed to load data
				</div>
				<button
					onClick={() => {
						setLocalLoading(true);
						Promise.all([
							fetchData('projects', true),
							fetchData('users', true),
						]).finally(() => setLocalLoading(false));
					}}
					className='px-4 py-2 bg-gradient-to-r from-primary to-accent text-white rounded hover:opacity-90 transition-colors btn-3d'>
					Retry
				</button>
			</div>
		);
	}

	const sendNotificationToAdmin = async (
		adminId,
		senderName,
		documentTitle,
	) => {
		try {
			const response = await fetchWithToken(
				`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/notifications/send-notification`,
				{
					method: 'POST',
					headers: { 'Content-Type': 'application/json' },
					body: JSON.stringify({
						recipientUid: adminId,
						senderName,
						messageContent: `${senderName} has accepted your document titled "${documentTitle}"`,
					}),
				},
			);
			if (!response.ok) throw new Error('Failed to send notification');
		} catch (error) {
			console.error('Error sending notification:', error);
		}
	};

	return (
		<div className='p-4 sm:p-6 bg-background rounded-xl shadow-xl max-w-full card'>
			<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 border-b border-primary/20 pb-4'>
				<h2 className='text-2xl sm:text-3xl font-bold text-primary mb-4 sm:mb-0'>
					Submitted Documents
				</h2>
				<div className='flex flex-col sm:flex-row gap-3 w-full sm:w-auto'>
					<button
						onClick={isSubscribed ? handleUnsubscribe : registerServiceWorker}
						className='flex items-center gap-2 bg-background border border-primary/30 text-text px-4 py-2 rounded-lg hover:bg-primary/10 transition w-full sm:w-auto'>
						{isSubscribed ? (
							<FaBellSlash className='text-accent' />
						) : (
							<FaBell className='text-accent' />
						)}
						{isSubscribed ? 'Unsubscribe' : 'Subscribe'}
					</button>
					<button
						onClick={handleRefreshData}
						className='flex items-center gap-2 bg-gradient-to-r from-primary to-accent text-white px-4 py-2 rounded-lg hover:opacity-90 transition w-full sm:w-auto btn-3d'
						disabled={refreshing}>
						<FaSync className={refreshing ? 'animate-spin' : ''} />
						{refreshing ? 'Refreshing...' : 'Refresh'}
					</button>
				</div>
			</div>

			<AdminStats
				user={user}
				isSubscribed={isSubscribed}
				handleUnsubscribe={handleUnsubscribe}
				registerServiceWorker={registerServiceWorker}
				getLoggedInAdminProjectCount={getLoggedInAdminProjectCount}
			/>

			<div className='mb-6 flex flex-col sm:flex-row gap-4 items-start sm:items-center bg-primary/5 p-4 rounded-lg'>
				<div className='relative w-full sm:flex-1'>
					<FaSearch className='absolute left-3 top-1/2 transform -translate-y-1/2 text-primary' />
					<input
						type='text'
						placeholder='Search by title or student name...'
						onChange={(e) => debouncedSearch(e.target.value)}
						className='w-full pl-10 pr-4 py-2 border border-primary/30 rounded-lg focus:ring-2 focus:ring-accent bg-background text-text'
						aria-label='Search projects'
					/>
				</div>
				<div className='flex flex-col sm:flex-row gap-4 w-full sm:w-auto'>
					<select
						value={statusFilter}
						onChange={(e) => setStatusFilter(e.target.value)}
						className='border border-primary/30 rounded-lg px-3 py-2 focus:ring-2 focus:ring-accent bg-background text-text w-full sm:w-auto'
						aria-label='Filter by status'>
						<option value='all'>All Status</option>
						<option value='pending'>Pending</option>
						<option value='accepted'>Accepted</option>
					</select>
					<select
						value={sortOrder}
						onChange={(e) => setSortOrder(e.target.value)}
						className='border border-primary/30 rounded-lg px-3 py-2 focus:ring-2 focus:ring-accent bg-background text-text w-full sm:w-auto'
						aria-label='Sort order'>
						<option value='desc'>Newest First</option>
						<option value='asc'>Oldest First</option>
					</select>
				</div>
			</div>

			<div className='overflow-x-auto'>
				{filteredProjects.length === 0 ? (
					<div className='text-center py-8 text-gray-500'>
						<FaProjectDiagram className='mx-auto text-4xl mb-2' />
						No projects found
					</div>
				) : (
					<table className='min-w-full bg-background border border-primary/20 rounded-lg'>
						<thead className='bg-primary/10'>
							<tr>
								<th className='py-3 px-4 text-left text-xs font-medium text-primary uppercase'>
									Title
								</th>
								<th className='py-3 px-4 text-left text-xs font-medium text-primary uppercase'>
									Student
								</th>
								<th className='py-3 px-4 text-left text-xs font-medium text-primary uppercase'>
									ID Number
								</th>
								<th className='py-3 px-4 text-left text-xs font-medium text-primary uppercase'>
									Price/ Pages
								</th>
								<th className='py-3 px-4 text-left text-xs font-medium text-primary uppercase'>
									Submitted On
								</th>
								{user?.superAdmin && (
									<th className='py-3 px-4 text-left text-xs font-medium text-primary uppercase'>
										Assigned To
									</th>
								)}
								<th className='py-3 px-4 text-left text-xs font-medium text-primary uppercase'>
									Status
								</th>
								<th className='py-3 px-4 text-left text-xs font-medium text-primary uppercase'>
									File Type
								</th>
								<th className='py-3 px-4 text-right text-xs font-medium text-primary uppercase'>
									Actions
								</th>
							</tr>
						</thead>
						<tbody className='divide-y'>
							{filteredProjects.map((project) => (
								<tr
									key={project._id}
									className='hover:bg-primary hover:text-white'>
									<td className='py-4 px-4'>{project.title}</td>
									<td className='py-4 px-4'>{project.studentName}</td>
									<td className='py-4 px-4'>{project.matricNumber}</td>
									<td className='py-4 px-4'>
										{project?.price ? (
											<div className='flex flex-col'>
												<span className='font-medium'>
													₦{project?.price?.toLocaleString() || 'No price'}/{' '}
													{project?.pageCount}
												</span>
												{project?.discountPercentage &&
												project.discountPercentage > 0 ? (
													<span className='text-xs text-green-600 dark:text-green-400'>
														(includes {project.discountPercentage}% discount)
													</span>
												) : (
													''
												)}
											</div>
										) : (
											'No Price'
										)}
									</td>
									<td className='py-4 px-4'>{formatDate(project.createdAt)}</td>
									{user?.superAdmin && (
										<td className='py-4 px-4'>
											{project.assignedAdmin
												? adminNameById.get(project.assignedAdmin) ??
												  'Unassigned'
												: 'Unassigned'}
										</td>
									)}
									<td className='py-4 px-4'>
										<span
											className={`px-2 py-1 rounded-full text-xs ${getStatusBadgeClass(
												project.status,
											)}`}>
											{project.status}
										</span>
									</td>
									<td className='py-4 px-4'>
										{MIME_TO_FORMAT[project.fileType] || 'Unknown'}
									</td>
									<td className='py-4 px-4 text-right flex justify-end gap-2'>
										{project.status === 'accepted' && (
											<>
												<button
													onClick={() =>
														handleView(project.fileUrl, project.fileType)
													}
													className='bg-primary/10 text-primary px-2 py-1 rounded hover:bg-primary/20 transition-colors'
													aria-label='View document'>
													<FaEye />
												</button>
												<button
													onClick={() =>
														handleDownload(
															project.fileUrl,
															project.matricNumber,
															project.title,
															project.fileType,
														)
													}
													className='bg-gradient-to-r from-primary to-accent text-white px-2 py-1 rounded hover:opacity-90 transition-colors'
													aria-label='Download document'>
													<FaDownload />
												</button>
											</>
										)}
										{user?.isAdmin && project.status === 'pending' && (
											<>
												<button
													onClick={async () => {
														handleAccept(project._id);
														await sendNotificationToAdmin(
															user.id,
															user.name,
															project.title,
														);
													}}
													disabled={!!processingProjects[project._id]}
													className='bg-green-500 text-white px-2 py-1 rounded hover:bg-green-600 disabled:opacity-50 transition-colors'
													aria-label='Accept project'>
													{processingProjects[project._id] === 'accepting' ? (
														<FaSpinner className='animate-spin' />
													) : (
														<FaCheck />
													)}
												</button>
												<button
													onClick={() => setProjectToReject(project)}
													disabled={!!processingProjects[project._id]}
													className='bg-red-500 text-white px-2 py-1 rounded hover:bg-red-600 disabled:opacity-50 transition-colors'
													aria-label='Reject project'>
													<FaTimes />
												</button>
											</>
										)}
										{user?.isAdmin && project.status !== 'pending' && (
											<button
												onClick={() => setProjectToDelete(project)}
												disabled={!!processingProjects[project._id]}
												className='bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 px-2 py-1 rounded hover:bg-red-200 dark:hover:bg-red-900/50 disabled:opacity-50 transition-colors'
												aria-label='Delete project'>
												{processingProjects[project._id] === 'deleting' ? (
													<FaSpinner className='animate-spin' />
												) : (
													<FaTrash />
												)}
											</button>
										)}
									</td>
								</tr>
							))}
						</tbody>
					</table>
				)}
			</div>

			{/* Pagination */}
			{!loading.projects && (
				<div className='mt-6'>
					<Pagination
						pagination={projectPagination}
						onPageChange={handlePageChange}
						onLimitChange={handleLimitChange}
						itemName='documents'
						currentItems={filteredProjects || []}
					/>
				</div>
			)}

			{/* Delete confirmation dialog */}
			<AlertDialog
				open={!!projectToDelete}
				onOpenChange={() => setProjectToDelete(null)}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Confirm Delete</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete "{projectToDelete?.title}"?
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={() => handleDelete(projectToDelete?._id)}
							className='bg-red-500 hover:bg-red-600 text-white'>
							Delete
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>

			{/* Reject confirmation dialog */}
			<AlertDialog
				open={!!projectToReject}
				onOpenChange={() => setProjectToReject(null)}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Confirm Rejection</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to reject "{projectToReject?.title}"?
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={() => handleReject(projectToReject?._id)}
							className='bg-red-500 hover:bg-red-600 text-white'>
							Reject
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</div>
	);
};

export default ProjectList;
